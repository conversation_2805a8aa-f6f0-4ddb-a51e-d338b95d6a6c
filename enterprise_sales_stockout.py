#!/usr/bin/env python3
"""
旺店通企业版销售出库单查询模块
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from wdt_enterprise_client import WDTEnterpriseClient, WDTEnterpriseAPIException
from config import WDTConfig


class EnterpriseSalesStockoutQuery:
    """旺店通企业版销售出库单查询类"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化查询器
        
        Args:
            config: 配置类，如果为None则使用默认配置
        """
        self.client = WDTEnterpriseClient(config)
        self.logger = logging.getLogger(__name__)
    
    def query_sales_stockout(
        self,
        start_time: datetime,
        end_time: datetime,
        status: Optional[int] = None,
        page_size: int = 100,
        page_no: int = 0,
        warehouse_no: Optional[str] = None,
        shop_no: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询销售出库单
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status: 出库单状态（可选）
                - 5: 已取消
                - 55: 已审核
                - 95: 已发货
                - 105: 部分打款
                - 110: 已完成
                - 113: 异常发货
            page_size: 每页大小（1-100）
            page_no: 页码（从0开始）
            warehouse_no: 仓库编号（可选）
            shop_no: 店铺编号（可选）
            
        Returns:
            查询结果
            
        Raises:
            WDTEnterpriseAPIException: API调用失败
        """
        try:
            # 验证时间跨度（最大30天）
            time_diff = end_time - start_time
            if time_diff.days > 30:
                raise ValueError("时间跨度不能超过30天")
            
            # 构建查询参数
            params = {
                'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
                'page_no': page_no,
                'page_size': min(page_size, 100)  # 限制最大100
            }
            
            # 添加可选参数
            if status is not None:
                params['status'] = status
            if warehouse_no:
                params['warehouse_no'] = warehouse_no
            if shop_no:
                params['shop_no'] = shop_no
            
            self.logger.info(f"查询销售出库单: {params}")
            
            # 调用API
            response = self.client.query_sales_stockout(
                start_time=params['start_time'],
                end_time=params['end_time'],
                page_no=params['page_no'],
                page_size=params['page_size'],
                status=params.get('status')
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"查询销售出库单失败: {e}")
            raise
    
    def query_all_pages(
        self,
        start_time: datetime,
        end_time: datetime,
        status: Optional[int] = None,
        page_size: int = 100,
        max_pages: Optional[int] = None,
        warehouse_no: Optional[str] = None,
        shop_no: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        查询所有页面的销售出库单
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status: 出库单状态（可选）
            page_size: 每页大小
            max_pages: 最大页数（可选，用于限制查询）
            warehouse_no: 仓库编号（可选）
            shop_no: 店铺编号（可选）
            
        Returns:
            所有出库单列表
        """
        all_stockouts = []
        page_no = 0
        
        while True:
            try:
                # 查询当前页
                response = self.query_sales_stockout(
                    start_time=start_time,
                    end_time=end_time,
                    status=status,
                    page_size=page_size,
                    page_no=page_no,
                    warehouse_no=warehouse_no,
                    shop_no=shop_no
                )
                
                # 获取出库单列表
                stockout_list = response.get('stockout_list', [])
                if not stockout_list:
                    break
                
                all_stockouts.extend(stockout_list)
                
                # 检查是否还有更多页面
                total_count = response.get('total_count', 0)
                if page_no == 0:
                    self.logger.info(f"总记录数: {total_count}")
                
                # 计算是否还有下一页
                current_count = (page_no + 1) * page_size
                if current_count >= total_count:
                    break
                
                # 检查最大页数限制
                if max_pages and page_no + 1 >= max_pages:
                    self.logger.warning(f"达到最大页数限制: {max_pages}")
                    break
                
                page_no += 1
                
            except Exception as e:
                self.logger.error(f"查询第{page_no}页失败: {e}")
                break
        
        self.logger.info(f"共查询到 {len(all_stockouts)} 条出库单记录")
        return all_stockouts
    
    def query_by_time_segments(
        self,
        start_time: datetime,
        end_time: datetime,
        segment_hours: int = 24,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        按时间段分段查询（用于处理大时间跨度）
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            segment_hours: 每段小时数
            **kwargs: 其他查询参数
            
        Returns:
            所有出库单列表
        """
        all_stockouts = []
        current_start = start_time
        
        while current_start < end_time:
            # 计算当前段的结束时间
            current_end = min(
                current_start + timedelta(hours=segment_hours),
                end_time
            )
            
            self.logger.info(f"查询时间段: {current_start} - {current_end}")
            
            try:
                # 查询当前时间段
                segment_stockouts = self.query_all_pages(
                    start_time=current_start,
                    end_time=current_end,
                    **kwargs
                )
                
                all_stockouts.extend(segment_stockouts)
                
            except Exception as e:
                self.logger.error(f"查询时间段失败 {current_start} - {current_end}: {e}")
            
            # 移动到下一个时间段
            current_start = current_end
        
        self.logger.info(f"分段查询完成，共 {len(all_stockouts)} 条记录")
        return all_stockouts
    
    def get_stockout_details(self, stockout_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取出库单详情
        
        Args:
            stockout_list: 出库单列表
            
        Returns:
            详情列表
        """
        details = []
        
        for stockout in stockout_list:
            stockout_info = {
                'stockout_id': stockout.get('stockout_id'),
                'order_no': stockout.get('order_no'),
                'src_order_no': stockout.get('src_order_no'),
                'warehouse_no': stockout.get('warehouse_no'),
                'warehouse_name': stockout.get('warehouse_name'),
                'shop_no': stockout.get('shop_no'),
                'shop_name': stockout.get('shop_name'),
                'status': stockout.get('status'),
                'trade_status': stockout.get('trade_status'),
                'consign_time': stockout.get('consign_time'),
                'goods_count': stockout.get('goods_count'),
                'goods_total_amount': stockout.get('goods_total_amount'),
                'receiver_name': stockout.get('receiver_name'),
                'receiver_mobile': stockout.get('receiver_mobile'),
                'logistics_no': stockout.get('logistics_no'),
                'logistics_name': stockout.get('logistics_name'),
                'details_list': stockout.get('details_list', [])
            }
            details.append(stockout_info)
        
        return details
