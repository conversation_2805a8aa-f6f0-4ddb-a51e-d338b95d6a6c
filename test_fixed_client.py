#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的客户端
"""

import json
from wdt_original_client import WDTOriginalClient

def test_fixed_client():
    """测试修复后的客户端"""
    
    print("=== 测试修复后的客户端 ===\n")
    
    # 创建客户端
    client = WDTOriginalClient()
    
    print("1. 测试 sales.trade.query 接口:")
    try:
        result = client.query_sales_trades(
            start_time='2025-07-04 11:00:00',
            end_time='2025-07-04 12:00:00',
            trade_status=30,
            page_no=0,
            page_size=10
        )
        
        print(f"✅ 成功调用接口！")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        
        # 检查是否是权限问题
        if "权限" in str(e) or "permission" in str(e).lower():
            print("🔑 这是权限问题，接口存在但需要申请权限")
        elif "接口【】名字错误" in str(e):
            print("❌ 仍然是接口名字错误，需要进一步调试")
        else:
            print("❓ 其他错误，需要分析")
    
    print("\n2. 测试其他可能的接口:")
    test_interfaces = [
        'trade.query',
        'order.query', 
        'goods.query',
        'warehouse.query'
    ]
    
    for interface in test_interfaces:
        print(f"\n测试接口: {interface}")
        try:
            result = client.call_api(interface, {
                'start_time': '2025-07-04 11:00:00',
                'end_time': '2025-07-04 12:00:00'
            })
            print(f"✅ 接口 {interface} 调用成功: {result}")
        except Exception as e:
            error_msg = str(e)
            if "权限" in error_msg:
                print(f"🔑 接口 {interface} 存在但需要权限")
            elif "接口【】名字错误" in error_msg:
                print(f"❌ 接口 {interface} 不存在")
            else:
                print(f"❓ 接口 {interface} 其他错误: {error_msg}")

if __name__ == "__main__":
    test_fixed_client()
