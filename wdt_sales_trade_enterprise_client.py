#!/usr/bin/env python3
"""
旺店通销售订单查询客户端（企业版API）
基于企业版API格式
"""
import logging
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

from wdt_enterprise_signature import WDTEnterpriseSignature
from config import WDTConfig


class WDTSalesTradeEnterpriseAPIException(Exception):
    """旺店通销售订单API异常"""
    
    def __init__(self, message: str, code: int = None):
        self.message = message
        self.code = code
        super().__init__(f"API Error {code}: {message}" if code else message)


class WDTSalesTradeEnterpriseClient:
    """旺店通销售订单查询客户端（企业版）"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化客户端
        
        Args:
            config: 配置类，如果为None则使用默认配置
        """
        self.config = config or WDTConfig
        self.logger = logging.getLogger(__name__)
        
        # 创建session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WDT-SalesTrade-Enterprise-Client/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        # 设置超时
        self.timeout = 30
        
        # API基础URL
        self.base_url = "https://api.wangdian.cn/openapi2/"
        
    def call_api(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用API
        
        Args:
            endpoint: API端点（如 'sales_trade_query.php'）
            params: 业务参数
            
        Returns:
            API响应数据
            
        Raises:
            WDTSalesTradeEnterpriseAPIException: API调用失败
        """
        try:
            # 构建完整的请求参数
            request_params = WDTEnterpriseSignature.build_request_params(
                params=params,
                sid=self.config.SID,
                appkey=self.config.APP_KEY,
                app_secret=self.config.APP_SECRET
            )
            
            self.logger.debug(f"请求参数: {json.dumps(request_params, ensure_ascii=False, indent=2)}")
            
            # 构建完整URL
            url = self.base_url + endpoint
            self.logger.debug(f"请求URL: {url}")
            
            # 发送POST请求
            response = self.session.post(
                url,
                data=request_params,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            response_data = response.json()
            
            self.logger.debug(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查API状态码
            if response_data.get('code') != 0:
                raise WDTSalesTradeEnterpriseAPIException(
                    code=response_data.get('code', -1),
                    message=response_data.get('message', '未知错误')
                )
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise WDTSalesTradeEnterpriseAPIException(f"HTTP请求失败: {e}", -1)
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            raise WDTSalesTradeEnterpriseAPIException(f"JSON解析失败: {e}", -1)
        except Exception as e:
            self.logger.error(f"API调用失败: {e}")
            raise WDTSalesTradeEnterpriseAPIException(f"API调用失败: {e}", -1)
    
    def query_sales_trades(
        self,
        start_time: str,
        end_time: str,
        page_no: int = 0,
        page_size: int = 100,
        trade_status: Optional[int] = None,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询销售订单
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            page_no: 页码（从0开始）
            page_size: 每页大小
            trade_status: 订单状态（可选）
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            
        Returns:
            查询结果
        """
        params = {
            'start_time': start_time,
            'end_time': end_time,
            'page_no': page_no,
            'page_size': page_size
        }

        if trade_status is not None:
            params['trade_status'] = trade_status
        if owner_no:
            params['owner_no'] = owner_no
        if warehouse_no:
            params['warehouse_no'] = warehouse_no

        # 尝试不同的端点
        endpoints_to_try = [
            'sales_trade_query.php',
            'trade_query.php',
            'sales_order_query.php',
            'order_query.php'
        ]
        
        last_exception = None
        
        for endpoint in endpoints_to_try:
            try:
                self.logger.info(f"尝试端点: {endpoint}")
                result = self.call_api(endpoint, params)
                self.logger.info(f"端点 {endpoint} 调用成功")
                return result
            except WDTSalesTradeEnterpriseAPIException as e:
                self.logger.warning(f"端点 {endpoint} 调用失败: {e}")
                last_exception = e
                continue
        
        # 如果所有端点都失败，抛出最后一个异常
        if last_exception:
            raise last_exception
        else:
            raise WDTSalesTradeEnterpriseAPIException("所有端点都调用失败", -1)
    
    def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            连接是否成功
        """
        try:
            # 使用一个简单的查询来测试连接
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=1)
            
            self.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                page_size=1
            )
            return True
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
    
    def get_available_endpoints(self) -> List[str]:
        """
        获取可用的端点列表
        
        Returns:
            可用端点列表
        """
        endpoints_to_test = [
            'sales_trade_query.php',
            'trade_query.php', 
            'sales_order_query.php',
            'order_query.php',
            'stockout_order_query_trade.php',  # 已知可用的端点
        ]
        
        available_endpoints = []
        
        # 使用简单参数测试每个端点
        test_params = {
            'start_time': (datetime.now() - timedelta(minutes=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'page_no': 0,
            'page_size': 1
        }
        
        for endpoint in endpoints_to_test:
            try:
                self.call_api(endpoint, test_params)
                available_endpoints.append(endpoint)
                self.logger.info(f"端点 {endpoint} 可用")
            except Exception as e:
                self.logger.debug(f"端点 {endpoint} 不可用: {e}")
        
        return available_endpoints
