"""
旺店通WMS API配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class WDTConfig:
    """旺店通WMS API配置类"""
    
    # API基本配置
    SID = os.getenv('WDT_SID', 'changhe')
    APP_KEY = os.getenv('WDT_APP_KEY', 'changhe_zy_wdt')
    APP_SECRET = os.getenv('WDT_APP_SECRET', '12684d858891369835a8ab7aa3bc660f')
    API_URL = os.getenv('WDT_API_URL', 'https://openapi.wdtwms.com/open_api/service.php')

    @classmethod
    def get_secret_and_salt(cls):
        """
        解析APP_SECRET获取secret和salt
        如果APP_SECRET包含冒号，则冒号前为secret，冒号后为salt
        否则整个APP_SECRET作为secret，salt为空
        """
        if ':' in cls.APP_SECRET:
            secret, salt = cls.APP_SECRET.split(':', 1)
            return secret, salt
        else:
            return cls.APP_SECRET, ''
    
    # 默认配置
    DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', '100'))
    DEFAULT_WAREHOUSE_NO = os.getenv('DEFAULT_WAREHOUSE_NO', '')
    DEFAULT_SHOP_NOS = os.getenv('DEFAULT_SHOP_NOS', '')
    
    # API版本
    API_VERSION = '1.0'
    
    # 请求超时时间（秒）
    REQUEST_TIMEOUT = 30
    
    # 重试次数
    MAX_RETRIES = 3
    
    @classmethod
    def validate_config(cls):
        """验证配置是否完整"""
        required_fields = ['SID', 'APP_KEY', 'APP_SECRET', 'API_URL']
        missing_fields = []
        
        for field in required_fields:
            if not getattr(cls, field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"缺少必要的配置项: {', '.join(missing_fields)}")
        
        return True
