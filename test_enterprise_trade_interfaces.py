#!/usr/bin/env python3
"""
测试企业版API的销售订单查询接口
"""
import logging
from datetime import datetime, timedelta

from wdt_enterprise_client import WDTEnterpriseClient, WDTEnterpriseAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_enterprise_trade_interfaces():
    """测试企业版API的销售订单查询接口"""
    print("测试企业版API的销售订单查询接口")
    print("=" * 60)
    
    # 可能的销售订单查询接口（基于企业版命名规律）
    possible_interfaces = [
        # 基于出库单接口 stockout_order_query_trade.php 的命名规律
        "sales_trade_query.php",
        "trade_query.php", 
        "order_query.php",
        "sales_order_query.php",
        
        # 可能的变体
        "trade_order_query.php",
        "sales_trade_order_query.php",
        "wms_trade_query.php",
        "wms_order_query.php",
        
        # 基于销售的变体
        "sales_query.php",
        "sales_list.php",
        "trade_list.php",
        "order_list.php",
        
        # 其他可能的命名
        "trade_search.php",
        "order_search.php",
        "sales_search.php",
        
        # 基于已知成功接口的变体
        "stockin_order_query_trade.php",  # 入库单
        "purchase_order_query.php",       # 采购单
        "refund_order_query.php",         # 退款单
    ]
    
    client = WDTEnterpriseClient()
    
    # 准备测试参数
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    test_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'page_no': 0,
        'page_size': 1
    }
    
    successful_interfaces = []
    permission_denied_interfaces = []
    not_found_interfaces = []
    other_error_interfaces = []
    
    print(f"测试时间范围: {test_params['start_time']} - {test_params['end_time']}")
    print(f"总共测试 {len(possible_interfaces)} 个接口")
    print()
    
    for i, interface in enumerate(possible_interfaces, 1):
        print(f"[{i:2d}/{len(possible_interfaces)}] 测试接口: {interface}")
        
        try:
            response = client.call_api(interface, test_params)
            
            # 成功调用
            print(f"  ✓ 调用成功")
            print(f"  响应代码: {response.get('code', 'N/A')}")
            
            # 检查响应内容
            if response.get('code') == 0:
                print(f"  ✓ 业务成功")
                data = response.get('data', {})
                if data:
                    print(f"  数据类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"  数据键: {list(data.keys())}")
                    elif isinstance(data, list):
                        print(f"  数据长度: {len(data)}")
                successful_interfaces.append(interface)
            else:
                error_msg = response.get('message', '未知错误')
                print(f"  ⚠ 业务错误: {error_msg}")
                if "权限" in error_msg:
                    permission_denied_interfaces.append(interface)
                else:
                    other_error_interfaces.append(interface)
            
            print()
            
        except WDTEnterpriseAPIException as e:
            error_code = e.code
            error_message = e.message
            
            if error_code == 1090 or "权限" in error_message:
                print(f"  ⚠ 权限问题: {error_message}")
                permission_denied_interfaces.append(interface)
            elif "404" in str(e) or "Not Found" in error_message:
                print(f"  ✗ 接口不存在: {error_message}")
                not_found_interfaces.append(interface)
            elif "参数" in error_message:
                print(f"  ⚠ 参数问题: {error_message}")
                # 参数问题可能意味着接口存在但参数不对
                permission_denied_interfaces.append(interface)
            else:
                print(f"  ✗ 其他错误: {error_message}")
                other_error_interfaces.append(interface)
        
        except Exception as e:
            print(f"  ✗ 异常: {e}")
            if "404" in str(e):
                not_found_interfaces.append(interface)
            else:
                other_error_interfaces.append(interface)
    
    # 输出总结
    print("=" * 60)
    print("企业版API测试总结:")
    print()
    
    if successful_interfaces:
        print(f"✓ 成功调用的接口 ({len(successful_interfaces)} 个):")
        for interface in successful_interfaces:
            print(f"  - {interface}")
        print()
    
    if permission_denied_interfaces:
        print(f"⚠ 存在但权限不足的接口 ({len(permission_denied_interfaces)} 个):")
        for interface in permission_denied_interfaces:
            print(f"  - {interface}")
        print()
    
    if not_found_interfaces:
        print(f"✗ 不存在的接口 ({len(not_found_interfaces)} 个):")
        for interface in not_found_interfaces:
            print(f"  - {interface}")
        print()
    
    if other_error_interfaces:
        print(f"❓ 其他错误的接口 ({len(other_error_interfaces)} 个):")
        for interface in other_error_interfaces:
            print(f"  - {interface}")
        print()
    
    # 推荐
    if successful_interfaces:
        print("🎯 推荐使用:")
        print(f"  接口名称: {successful_interfaces[0]}")
        return successful_interfaces[0]
    elif permission_denied_interfaces:
        print("🔑 需要申请权限:")
        print(f"  接口名称: {permission_denied_interfaces[0]}")
        print("  请在旺店通开放平台申请该接口的调用权限")
        return permission_denied_interfaces[0]
    else:
        print("❌ 未找到可用的销售订单查询接口")
        return None


def test_known_working_interface():
    """测试已知可用的出库单接口作为对比"""
    print("\n=== 测试已知可用的出库单接口（作为对比） ===")
    
    client = WDTEnterpriseClient()
    
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    test_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'page_no': 0,
        'page_size': 1
    }
    
    try:
        response = client.call_api("stockout_order_query_trade.php", test_params)
        print("✓ 出库单接口调用成功")
        print(f"  响应代码: {response.get('code', 'N/A')}")
        print(f"  响应消息: {response.get('message', 'N/A')}")
        
        if response.get('code') == 1090:
            print("  → 权限错误，这是正常的（需要申请权限）")
        
    except Exception as e:
        print(f"✗ 出库单接口调用失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试企业版API的销售订单查询接口...")
    print()
    
    # 测试已知可用的接口作为对比
    test_known_working_interface()
    
    # 测试所有可能的销售订单查询接口
    best_interface = test_enterprise_trade_interfaces()
    
    print("\n测试完成！")
    
    if best_interface:
        print(f"\n🎯 建议使用接口: {best_interface}")
        print("📝 下一步:")
        print("1. 在旺店通开放平台申请该接口权限")
        print("2. 使用企业版API客户端调用该接口")
    else:
        print("\n💡 建议:")
        print("1. 检查旺店通开放平台的API文档")
        print("2. 联系旺店通技术支持确认销售订单查询接口名称")
        print("3. 确认您的账号是否有权限访问销售订单查询功能")


if __name__ == "__main__":
    main()
