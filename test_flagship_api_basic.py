#!/usr/bin/env python3
"""
旺店通旗舰版API基础连接测试
"""
import logging
from datetime import datetime, timedelta

from wdt_client import WDTClient
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_flagship_api_basic():
    """测试旗舰版API基础连接"""
    print("旺店通旗舰版API基础连接测试")
    print("=" * 60)
    
    try:
        # 验证配置
        config = WDTConfig
        print("✓ 配置验证通过")
        print(f"  SID: {config.SID}")
        print(f"  APP_KEY: {config.APP_KEY}")
        print()
        
        # 创建旗舰版客户端 - 使用正确的旗舰版API端点
        client = WDTClient(
            sid=config.SID,
            app_key=config.APP_KEY,
            app_secret=config.APP_SECRET,
            api_url="http://wdt.wangdian.cn/openapi"
        )
        print("✓ 旗舰版客户端创建成功")
        print()
        
        # 测试一些已知的接口（使用旗舰版格式）
        test_methods = [
            'finance.settle.Logistics.search',  # 电子面单号查询（已知存在的接口）
            'wms.stockout.sales.query',         # 销售出库单查询
            'sales.trade.query',                # 销售订单查询
            'trade.query',                      # 订单查询
            'wms.trade.query',                  # WMS订单查询
        ]
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        for method in test_methods:
            print(f"=== 测试接口: {method} ===")
            
            try:
                # 构建测试参数
                params = {
                    'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'trade_status': 30
                }
                
                # 调用API
                response = client.call_api(
                    method=method,
                    params=params,
                    page_size=5,
                    page_no=0
                )
                
                print(f"  ✓ 接口 '{method}' 调用成功")
                print(f"  响应状态: {response.get('flag', 'unknown')}")
                
                if response.get('flag') == 'success':
                    data = response.get('data', {})
                    total = data.get('total_count', 0)
                    print(f"  总记录数: {total}")
                    return method  # 返回第一个成功的接口
                else:
                    error_code = response.get('code', 'unknown')
                    error_message = response.get('message', '未知错误')
                    print(f"  业务错误: {error_code} - {error_message}")
                
            except Exception as e:
                print(f"  ✗ 接口 '{method}' 调用失败: {e}")
            
            print()
        
        print("❌ 所有测试接口都未成功")
        return None
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return None


def test_api_with_different_params():
    """测试不同的参数格式"""
    print("=== 测试不同参数格式 ===")
    
    config = WDTConfig
    client = WDTClient(
        sid=config.SID,
        app_key=config.APP_KEY,
        app_secret=config.APP_SECRET,
        api_url="http://wdt.wangdian.cn/openapi"
    )
    
    # 测试最简单的参数
    simple_params = {}
    
    try:
        print("测试空参数...")
        response = client.call_api(
            method='sales.trade.query',
            params=simple_params,
            page_size=1,
            page_no=0
        )
        
        print(f"响应: {response}")
        
    except Exception as e:
        print(f"空参数测试失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通旗舰版API基础连接...")
    print()
    
    # 基础连接测试
    success_method = test_flagship_api_basic()
    
    if success_method:
        print(f"✅ 找到可用接口: {success_method}")
    else:
        print("🔍 尝试不同参数格式...")
        test_api_with_different_params()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
