"""
旺店通API权限测试脚本
"""
import logging
from wdt_enterprise_client import WDTEnterpriseClient, WDTEnterpriseAPIException
from config import WDTConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

def test_permission():
    """测试API权限"""
    logger = logging.getLogger(__name__)
    
    try:
        # 验证配置
        WDTConfig.validate_config()
        logger.info("配置验证通过")
        
        # 创建企业版客户端
        client = WDTEnterpriseClient()
        
        # 测试连接和权限
        logger.info("测试API权限...")
        if client.test_connection():
            logger.info("API权限验证成功")
            print("API调用成功，权限验证通过")
            return True
        else:
            logger.error("API权限验证失败")
            print("API权限验证失败")
            return False
        
        # 检查响应
        if response.get('code') == 0:
            logger.info("API权限验证成功")
            print("API调用成功，权限验证通过")
            return True
        else:
            error_msg = response.get('message', '未知错误')
            logger.error(f"API权限验证失败: {error_msg}")
            print(f"API权限验证失败: {error_msg}")
            return False
            
    except WDTAPIException as e:
        logger.error(f"API调用异常: {e}")
        print(f"API调用异常: {e}")
        return False
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        print(f"程序执行异常: {e}")
        return False

if __name__ == "__main__":
    setup_logging()
    test_permission()
