#!/usr/bin/env python3
"""
测试旺店通可能的销售订单查询接口
"""
import logging
from datetime import datetime, timedelta

from wdt_original_client import WDTOriginalClient, WDTOriginalAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_trade_interfaces():
    """测试可能的销售订单查询接口"""
    print("测试旺店通可能的销售订单查询接口")
    print("=" * 60)
    
    # 可能的接口名称
    possible_interfaces = [
        # 基于文档提到的 sales.trade.query
        "sales.trade.query",
        
        # 常见的订单查询接口名称
        "trade.query",
        "order.query", 
        "sales.order.query",
        "wms.trade.query",
        "wms.order.query",
        
        # 其他可能的变体
        "trade.search",
        "order.search",
        "sales.trade.search",
        "sales.order.search",
        
        # 基于出库单接口的命名规律
        "trade.order.query",
        "sales.trade.order.query",
        
        # 可能的下划线格式
        "trade_query",
        "order_query",
        "sales_trade_query",
        "sales_order_query",
        
        # 可能的驼峰格式
        "tradeQuery",
        "orderQuery",
        "salesTradeQuery",
        "salesOrderQuery"
    ]
    
    client = WDTOriginalClient()
    
    # 准备测试参数
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    test_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'page_no': 0,
        'page_size': 1
    }
    
    successful_interfaces = []
    permission_denied_interfaces = []
    invalid_interfaces = []
    
    print(f"测试时间范围: {test_params['start_time']} - {test_params['end_time']}")
    print(f"总共测试 {len(possible_interfaces)} 个接口名称")
    print()
    
    for i, interface in enumerate(possible_interfaces, 1):
        print(f"[{i:2d}/{len(possible_interfaces)}] 测试接口: {interface}")
        
        try:
            response = client.call_api(interface, test_params)
            
            # 成功调用
            print(f"  ✓ 调用成功")
            print(f"  响应状态: {response.get('flag')}")
            
            content = response.get('content', {})
            if content:
                print(f"  内容类型: {type(content)}")
                if isinstance(content, dict):
                    print(f"  内容键: {list(content.keys())}")
                elif isinstance(content, list):
                    print(f"  内容长度: {len(content)}")
            
            successful_interfaces.append(interface)
            print()
            
        except WDTOriginalAPIException as e:
            error_code = e.code
            error_message = e.message
            
            if "权限" in error_message or "permission" in error_message.lower():
                print(f"  ⚠ 权限问题: {error_message}")
                permission_denied_interfaces.append(interface)
            elif "接口" in error_message and "名字" in error_message:
                print(f"  ✗ 接口不存在: {error_message}")
                invalid_interfaces.append(interface)
            elif "参数" in error_message:
                print(f"  ⚠ 参数问题: {error_message}")
                # 参数问题可能意味着接口存在但参数不对
                permission_denied_interfaces.append(interface)
            else:
                print(f"  ✗ 其他错误: {error_message}")
                invalid_interfaces.append(interface)
        
        except Exception as e:
            print(f"  ✗ 异常: {e}")
            invalid_interfaces.append(interface)
    
    # 输出总结
    print("=" * 60)
    print("测试总结:")
    print()
    
    if successful_interfaces:
        print(f"✓ 成功调用的接口 ({len(successful_interfaces)} 个):")
        for interface in successful_interfaces:
            print(f"  - {interface}")
        print()
    
    if permission_denied_interfaces:
        print(f"⚠ 存在但权限不足的接口 ({len(permission_denied_interfaces)} 个):")
        for interface in permission_denied_interfaces:
            print(f"  - {interface}")
        print()
    
    if invalid_interfaces:
        print(f"✗ 不存在的接口 ({len(invalid_interfaces)} 个):")
        for interface in invalid_interfaces:
            print(f"  - {interface}")
        print()
    
    # 推荐
    if successful_interfaces:
        print("🎯 推荐使用:")
        print(f"  接口名称: {successful_interfaces[0]}")
        return successful_interfaces[0]
    elif permission_denied_interfaces:
        print("🔑 需要申请权限:")
        print(f"  接口名称: {permission_denied_interfaces[0]}")
        print("  请在旺店通开放平台申请该接口的调用权限")
        return permission_denied_interfaces[0]
    else:
        print("❌ 未找到可用的销售订单查询接口")
        return None


def test_specific_interface_with_different_params(interface_name: str):
    """测试特定接口的不同参数组合"""
    print(f"\n=== 测试接口 {interface_name} 的不同参数组合 ===")
    
    client = WDTOriginalClient()
    
    # 不同的参数组合
    param_combinations = [
        # 基础参数
        {
            'start_time': (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'page_no': 0,
            'page_size': 1
        },
        
        # 添加状态参数
        {
            'start_time': (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'page_no': 0,
            'page_size': 1,
            'trade_status': 30
        },
        
        # 添加其他可能的参数
        {
            'start_time': (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'page_no': 0,
            'page_size': 1,
            'status': 30
        },
        
        # 使用不同的时间字段名
        {
            'begin_time': (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'page_no': 0,
            'page_size': 1
        }
    ]
    
    for i, params in enumerate(param_combinations, 1):
        print(f"\n参数组合 {i}:")
        print(f"  参数: {params}")
        
        try:
            response = client.call_api(interface_name, params)
            print(f"  ✓ 成功")
            
            content = response.get('content', {})
            if content:
                print(f"  返回数据: {type(content)}")
                if isinstance(content, dict) and content:
                    print(f"  数据键: {list(content.keys())}")
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通销售订单查询接口...")
    print()
    
    # 测试所有可能的接口
    best_interface = test_trade_interfaces()
    
    # 如果找到了可用的接口，进一步测试参数
    if best_interface:
        test_specific_interface_with_different_params(best_interface)
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
