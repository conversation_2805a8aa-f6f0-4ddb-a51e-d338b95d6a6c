#!/usr/bin/env python3
"""
测试旺店通销售订单查询接口（基于完整官方文档）
接口名称：sales.trade.query
"""
import logging
import json
from datetime import datetime, timedelta

from wdt_original_client import WDTOriginalClient, WDTOriginalAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_sales_trade_query():
    """测试销售订单查询接口"""
    print("测试旺店通销售订单查询接口（基于完整官方文档）")
    print("接口名称: sales.trade.query")
    print("=" * 70)
    
    try:
        # 验证配置
        config = WDTConfig
        print("✓ 配置验证通过")
        print(f"  SID: {config.SID}")
        print(f"  APP_KEY: {config.APP_KEY}")
        print()
        
        # 创建客户端
        client = WDTOriginalClient(config)
        print("✓ 原始API客户端创建成功")
        print(f"  API端点: {client.base_url}")
        print()
        
        # 测试不同的订单状态
        trade_statuses = [
            (30, "待审核"),
            (5, "已取消")
        ]
        
        for trade_status, status_name in trade_statuses:
            print(f"=== 测试订单状态: {trade_status} ({status_name}) ===")
            
            # 设置查询时间范围（最近1小时，符合1天限制）
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            try:
                response = client.query_sales_trades(
                    start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    trade_status=trade_status,
                    page_size=5  # 小批量测试
                )
                
                print("✓ 销售订单查询成功")
                
                # 解析响应（基于文档格式）
                content = response.get('content', [])
                total = response.get('total', 0)
                
                print(f"  总记录数: {total}")
                print(f"  当前页记录数: {len(content)}")
                
                # 显示订单详情
                if content:
                    print(f"\n订单详情（前3条）:")
                    for i, trade in enumerate(content[:3]):
                        print(f"  订单 {i+1}:")
                        print(f"    ERP单号: {trade.get('trade_no', 'N/A')}")
                        print(f"    原始单号: {trade.get('src_tids', 'N/A')}")
                        print(f"    仓储单号: {trade.get('src_order_no', 'N/A')}")
                        print(f"    订单状态: {trade.get('trade_status', 'N/A')}")
                        print(f"    货主编号: {trade.get('owner_no', 'N/A')}")
                        print(f"    仓库编号: {trade.get('warehouse_no', 'N/A')}")
                        print(f"    店铺名称: {trade.get('shop_name', 'N/A')}")
                        print(f"    买家昵称: {trade.get('buyer_nick', 'N/A')}")
                        print(f"    收货地址: {trade.get('receiver_area', 'N/A')}")
                        print(f"    订单金额: {trade.get('total_amount', 'N/A')}")
                        print(f"    货品数量: {trade.get('goods_count', 'N/A')}")
                        print(f"    货品种类: {trade.get('goods_type_count', 'N/A')}")
                        print(f"    接单时间: {trade.get('trade_create_time', 'N/A')}")
                        print(f"    平台下单时间: {trade.get('trade_time', 'N/A')}")
                        
                        # 显示货品明细
                        goods_detail = trade.get('goods_detail', [])
                        if goods_detail:
                            print(f"    货品明细 ({len(goods_detail)} 种):")
                            for j, goods in enumerate(goods_detail[:2]):  # 只显示前2种
                                print(f"      {j+1}. 商家编码: {goods.get('spec_no', 'N/A')}")
                                print(f"         数量: {goods.get('num', 'N/A')}")
                                print(f"         重量: {goods.get('weight', 'N/A')} kg")
                                print(f"         体积: {goods.get('volume', 'N/A')} ml")
                        print()
                else:
                    print("  未找到订单记录")
                
                print()
                return True
                
            except WDTOriginalAPIException as e:
                print(f"✗ 销售订单查询失败: {e}")
                
                # 分析错误类型
                if "权限" in str(e):
                    print("  → 权限问题：需要申请接口权限")
                elif "参数" in str(e):
                    print("  → 参数问题：检查参数格式")
                elif "时间" in str(e):
                    print("  → 时间问题：检查时间格式或范围")
                elif "接口" in str(e) and "名字" in str(e):
                    print("  → 接口名称问题：检查method参数")
                else:
                    print(f"  → 其他问题: {e}")
                
                print()
        
        return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_different_time_ranges():
    """测试不同的时间范围"""
    print("=== 测试不同时间范围 ===")
    
    client = WDTOriginalClient()
    
    # 测试不同的时间范围（都在1天限制内）
    time_ranges = [
        ("最近1小时", timedelta(hours=1)),
        ("最近6小时", timedelta(hours=6)),
        ("最近12小时", timedelta(hours=12)),
        ("最近24小时", timedelta(hours=24))  # 最大1天
    ]
    
    for name, delta in time_ranges:
        print(f"\n测试时间范围: {name}")
        
        end_time = datetime.now()
        start_time = end_time - delta
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,  # 待审核
                page_size=1
            )
            
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"  ✓ 成功，总记录数: {total}")
            
            if total > 0:
                return True  # 找到有数据的时间范围
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    return False


def test_pagination():
    """测试分页功能"""
    print("\n=== 测试分页功能 ===")
    
    client = WDTOriginalClient()
    
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=24)  # 最近24小时
    
    try:
        # 测试第一页
        response = client.query_sales_trades(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            trade_status=30,  # 待审核
            page_no=0,
            page_size=5
        )
        
        content = response.get('content', [])
        total = response.get('total', 0)
        
        print(f"第一页: {len(content)} 条记录，总计: {total} 条")
        
        # 如果有多页，测试第二页
        if total > 5:
            response2 = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,
                page_no=1,
                page_size=5
            )
            
            content2 = response2.get('content', [])
            print(f"第二页: {len(content2)} 条记录")
            
            # 验证total字段只在第一页返回
            total2 = response2.get('total')
            if total2 is None:
                print("✓ 验证通过：total字段只在第一页返回")
            else:
                print(f"第二页也返回了total: {total2}")
        
        return True
        
    except Exception as e:
        print(f"✗ 分页测试失败: {e}")
        return False


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通销售订单查询接口（基于完整官方文档）...")
    print()
    
    # 基础测试
    success = test_sales_trade_query()
    
    if success:
        # 如果基础测试成功，进行更多测试
        test_different_time_ranges()
        test_pagination()
    else:
        print("🔍 基础测试失败，检查接口可用性...")
    
    print("\n测试完成！")
    print("\n📖 接口说明:")
    print("- 接口名称: sales.trade.query")
    print("- 时间跨度: 最大1天")
    print("- 分页限制: 最大100条/页")
    print("- 压缩要求: 必须启用 compress_response_body=1")
    print("- 订单状态: 5=已取消, 30=待审核")


if __name__ == "__main__":
    main()
