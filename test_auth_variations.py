#!/usr/bin/env python3
"""
测试不同的认证参数格式
"""
import logging
import requests
import json
from datetime import datetime

from wdt_signature import WDTSignature
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_auth_variations():
    """测试不同的认证参数格式"""
    print("测试旺店通旗舰版API认证参数格式")
    print("=" * 60)
    
    config = WDTConfig
    base_url = "http://wdt.wangdian.cn/openapi"
    
    # 获取secret和salt
    secret, salt = config.get_secret_and_salt()
    
    print(f"配置信息:")
    print(f"  SID: {config.SID}")
    print(f"  APP_KEY: {config.APP_KEY}")
    print(f"  SECRET: {secret}")
    print(f"  SALT: {salt}")
    print()
    
    # 测试不同的参数组合
    test_cases = [
        {
            'name': '标准格式 (sid + key)',
            'params': {
                'sid': config.SID,
                'key': config.APP_KEY,
                'method': 'finance.settle.Logistics.search',
                'v': '1.0',
                'timestamp': WDTSignature.generate_timestamp(),
            }
        },
        {
            'name': '使用seller_id替代sid',
            'params': {
                'seller_id': config.SID,
                'key': config.APP_KEY,
                'method': 'finance.settle.Logistics.search',
                'v': '1.0',
                'timestamp': WDTSignature.generate_timestamp(),
            }
        },
        {
            'name': '使用app_key替代key',
            'params': {
                'sid': config.SID,
                'app_key': config.APP_KEY,
                'method': 'finance.settle.Logistics.search',
                'v': '1.0',
                'timestamp': WDTSignature.generate_timestamp(),
            }
        },
        {
            'name': '添加salt参数',
            'params': {
                'sid': config.SID,
                'key': config.APP_KEY,
                'salt': salt,
                'method': 'finance.settle.Logistics.search',
                'v': '1.0',
                'timestamp': WDTSignature.generate_timestamp(),
            }
        },
        {
            'name': '使用完整secret作为key',
            'params': {
                'sid': config.SID,
                'key': config.APP_SECRET,  # 使用完整的secret
                'method': 'finance.settle.Logistics.search',
                'v': '1.0',
                'timestamp': WDTSignature.generate_timestamp(),
            }
        }
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'WDT-Test-Client/1.0',
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    })
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"=== 测试 {i}: {test_case['name']} ===")
        
        try:
            # 准备参数
            params = test_case['params'].copy()
            
            # 添加简单的业务参数
            params['params'] = json.dumps({
                'start_time': (datetime.now().replace(hour=datetime.now().hour-1)).strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 5
            })
            
            # 添加分页参数
            params['pager'] = json.dumps({
                'page_size': 1,
                'page_no': 0,
                'calc_total': 1
            })
            
            # 生成签名
            sign = WDTSignature.generate_sign(params, secret)
            params['sign'] = sign
            
            # 记录请求参数
            log_params = params.copy()
            log_params['sign'] = '***'
            print(f"请求参数: {json.dumps(log_params, ensure_ascii=False, indent=2)}")
            
            # 发送请求
            response = session.post(base_url, data=params, timeout=30)
            response.raise_for_status()
            
            # 解析响应
            data = json.loads(response.content.decode('utf-8'))
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # 检查结果
            status = data.get('status', -1)
            message = data.get('message', '')
            
            if status == 0:
                print(f"✓ 成功！找到正确的认证格式")
                return test_case['name'], params
            elif "卖家帐号" in message:
                print(f"✗ 仍然是卖家账号错误")
            elif "权限" in message:
                print(f"✓ 认证通过，但权限不足: {message}")
                return test_case['name'], params
            else:
                print(f"✗ 其他错误: {message}")
            
        except Exception as e:
            print(f"✗ 请求失败: {e}")
        
        print()
    
    print("❌ 所有认证格式都失败")
    return None, None


def test_minimal_request():
    """测试最小化请求"""
    print("=== 测试最小化请求 ===")
    
    config = WDTConfig
    base_url = "http://wdt.wangdian.cn/openapi"
    secret, salt = config.get_secret_and_salt()
    
    # 最小化参数
    params = {
        'sid': config.SID,
        'key': config.APP_KEY,
        'method': 'finance.settle.Logistics.search',
        'v': '1.0',
        'timestamp': WDTSignature.generate_timestamp(),
    }
    
    # 生成签名
    sign = WDTSignature.generate_sign(params, secret)
    params['sign'] = sign
    
    print(f"最小化请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
    
    try:
        session = requests.Session()
        response = session.post(base_url, data=params, timeout=30)
        response.raise_for_status()
        
        data = json.loads(response.content.decode('utf-8'))
        print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        return data.get('status') == 0 or "权限" in data.get('message', '')
        
    except Exception as e:
        print(f"请求失败: {e}")
        return False


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通旗舰版API认证...")
    print()
    
    # 测试不同的认证格式
    success_format, success_params = test_auth_variations()
    
    if success_format:
        print(f"✅ 找到有效的认证格式: {success_format}")
    else:
        print("🔍 尝试最小化请求...")
        if test_minimal_request():
            print("✅ 最小化请求成功")
        else:
            print("❌ 所有测试都失败")
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
