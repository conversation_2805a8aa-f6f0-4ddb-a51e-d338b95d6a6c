#!/usr/bin/env python3
"""
旺店通WMS销售订单查询客户端
基于 sales.trade.query 接口
"""
import logging
import requests
import json
import gzip
from datetime import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode

from wdt_signature import WDTSignature
from config import WDTConfig


class WDTSalesTradeAPIException(Exception):
    """旺店通销售订单API异常"""
    
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(f"API Error {code}: {message}" if code else message)


class WDTSalesTradeClient:
    """旺店通WMS销售订单查询客户端"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化客户端
        
        Args:
            config: 配置类，如果为None则使用默认配置
        """
        self.config = config or WDTConfig
        self.base_url = "https://openapi.wdtwms.com/open_api/service.php"
        self.logger = logging.getLogger(__name__)
        
        # 创建session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WDT-Sales-Trade-Client/1.0',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    
    def _prepare_params(self, business_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备请求参数（使用旗舰版奇门算法）

        Args:
            business_params: 业务参数

        Returns:
            完整的请求参数
        """
        # 获取secret和salt
        if ':' in self.config.APP_SECRET:
            secret, salt = self.config.APP_SECRET.split(':', 1)
        else:
            secret, salt = self.config.APP_SECRET, ''

        # 构建请求参数（使用旗舰版格式）
        request_params = WDTSignature.build_request_params(
            method='sales.trade.query',
            params=business_params,
            pager=None,  # 分页参数包含在业务参数中
            sid=self.config.SID,
            app_key=self.config.APP_KEY,
            app_secret=secret,
            salt=salt
        )

        # 添加压缩参数
        request_params['compress_response_body'] = 1

        return request_params
    
    def _make_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求（使用旗舰版格式）

        Args:
            params: 请求参数

        Returns:
            响应数据

        Raises:
            WDTSalesTradeAPIException: 请求失败
        """
        try:
            # 记录请求参数（隐藏敏感信息）
            log_params = params.copy()
            log_params['sign'] = '***'
            self.logger.debug(f"请求参数: {json.dumps(log_params, ensure_ascii=False, indent=2)}")

            # 发送POST请求 - 使用form-data格式
            # 需要将复杂参数转换为JSON字符串
            form_data = {}
            for key, value in params.items():
                if isinstance(value, (dict, list)):
                    form_data[key] = json.dumps(value, ensure_ascii=False)
                else:
                    form_data[key] = str(value)

            response = self.session.post(
                self.base_url,
                data=form_data,
                timeout=30
            )

            # 检查HTTP状态
            response.raise_for_status()

            # 处理压缩响应
            content = response.content
            if response.headers.get('Content-Encoding') == 'gzip':
                content = gzip.decompress(content)

            # 解析JSON
            try:
                data = json.loads(content.decode('utf-8'))
            except json.JSONDecodeError as e:
                raise WDTSalesTradeAPIException(f"响应JSON解析失败: {e}")

            self.logger.debug(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

            # 检查业务状态
            if data.get('flag') != 'success':
                error_code = data.get('code', 'unknown')
                error_message = data.get('message', '未知错误')
                raise WDTSalesTradeAPIException(error_message, error_code)

            return data

        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise WDTSalesTradeAPIException(f"HTTP请求失败: {e}")
        except Exception as e:
            if isinstance(e, WDTSalesTradeAPIException):
                raise
            self.logger.error(f"请求处理失败: {e}")
            raise WDTSalesTradeAPIException(f"请求处理失败: {e}")
    
    def query_sales_trades(
        self,
        start_time: str,
        end_time: str,
        trade_status: int = 30,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        is_exist_flag: Optional[int] = None,
        page_no: int = 0,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        查询销售订单
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            trade_status: 订单状态 (5=已取消, 30=待审核)
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            is_exist_flag: 标记过滤 (0=全部, 1=存在标记, 2=不存在标记)
            page_no: 页码（从0开始）
            page_size: 每页大小（最大100）
            
        Returns:
            查询结果
            
        Raises:
            WDTSalesTradeAPIException: API调用失败
        """
        # 验证时间跨度（最大1天）
        try:
            start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            time_diff = end_dt - start_dt
            if time_diff.days > 1:
                raise ValueError("时间跨度不能超过1天")
        except ValueError as e:
            raise WDTSalesTradeAPIException(f"时间参数错误: {e}")
        
        # 构建业务参数
        business_params = {
            'start_time': start_time,
            'end_time': end_time,
            'trade_status': trade_status,
            'page_no': page_no,
            'page_size': min(page_size, 100)  # 限制最大100
        }
        
        # 添加可选参数
        if owner_no:
            business_params['owner_no'] = owner_no
        if warehouse_no:
            business_params['warehouse_no'] = warehouse_no
        if is_exist_flag is not None:
            business_params['is_exist_flag'] = is_exist_flag
        
        # 准备完整参数
        params = self._prepare_params(business_params)

        # 发送请求
        return self._make_request(params)
    
    def get_trade_details(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        提取订单详情
        
        Args:
            response_data: API响应数据
            
        Returns:
            订单详情列表
        """
        content = response_data.get('content', [])
        details = []
        
        for trade in content:
            trade_info = {
                'trade_no': trade.get('trade_no'),
                'src_tids': trade.get('src_tids'),
                'src_order_no': trade.get('src_order_no'),
                'owner_no': trade.get('owner_no'),
                'warehouse_no': trade.get('warehouse_no'),
                'trade_status': trade.get('trade_status'),
                'shop_name': trade.get('shop_name'),
                'shop_no': trade.get('shop_no'),
                'buyer_nick': trade.get('buyer_nick'),
                'receiver_area': trade.get('receiver_area'),
                'total_amount': trade.get('total_amount'),
                'goods_count': trade.get('goods_count'),
                'goods_type_count': trade.get('goods_type_count'),
                'weight': trade.get('weight'),
                'trade_time': trade.get('trade_time'),
                'pay_time': trade.get('pay_time'),
                'trade_create_time': trade.get('trade_create_time'),
                'goods_detail': trade.get('goods_detail', [])
            }
            details.append(trade_info)
        
        return details
