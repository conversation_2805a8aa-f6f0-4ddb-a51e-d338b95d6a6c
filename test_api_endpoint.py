#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API端点和请求方式
"""

import json
import requests
import time
import hashlib
from urllib.parse import urlencode
from config import WDTConfig

def generate_sign(params, app_secret):
    """生成签名"""
    # 排序参数
    sorted_params = sorted(params.items())
    
    # 构建签名字符串
    param_str = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_str = f"{app_secret}{param_str}{app_secret}"
    
    # 生成MD5签名
    md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    return md5_hash.upper()

def test_api_endpoint():
    """测试API端点和请求方式"""
    
    print("=== 测试API端点和请求方式 ===\n")
    
    # 基础参数
    base_params = {
        'sid': WDTConfig.SID,
        'appkey': WDTConfig.APP_KEY,
        'sign_method': 'md5',
        'format': 'json',
        'timestamp': str(int(time.time())),
        'method': 'sales.trade.query'
    }
    
    # 生成签名
    sign = generate_sign(base_params, WDTConfig.APP_SECRET)
    base_params['sign'] = sign
    
    # 测试不同的请求方式
    test_cases = [
        {
            'name': '1. POST with form data',
            'method': 'POST',
            'url': 'https://openapi.wdtwms.com/open_api/service.php',
            'data': base_params,
            'headers': {'Content-Type': 'application/x-www-form-urlencoded'}
        },
        {
            'name': '2. POST with JSON data',
            'method': 'POST',
            'url': 'https://openapi.wdtwms.com/open_api/service.php',
            'json': base_params,
            'headers': {'Content-Type': 'application/json'}
        },
        {
            'name': '3. GET with query parameters',
            'method': 'GET',
            'url': f'https://openapi.wdtwms.com/open_api/service.php?{urlencode(base_params)}',
            'headers': {}
        },
        {
            'name': '4. POST without Content-Type',
            'method': 'POST',
            'url': 'https://openapi.wdtwms.com/open_api/service.php',
            'data': base_params,
            'headers': {}
        },
        {
            'name': '5. 测试端点 - 不带service.php',
            'method': 'POST',
            'url': 'https://openapi.wdtwms.com/open_api/',
            'data': base_params,
            'headers': {}
        },
        {
            'name': '6. 测试端点 - 直接到根目录',
            'method': 'POST',
            'url': 'https://openapi.wdtwms.com/',
            'data': base_params,
            'headers': {}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        
        try:
            if test_case['method'] == 'GET':
                response = requests.get(
                    test_case['url'],
                    headers=test_case['headers'],
                    timeout=30
                )
            else:
                if 'json' in test_case:
                    response = requests.post(
                        test_case['url'],
                        json=test_case['json'],
                        headers=test_case['headers'],
                        timeout=30
                    )
                else:
                    response = requests.post(
                        test_case['url'],
                        data=test_case['data'],
                        headers=test_case['headers'],
                        timeout=30
                    )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text[:500]}")
            
            # 尝试解析JSON
            try:
                response_data = response.json()
                print(f"解析后的响应: {json.dumps(response_data, ensure_ascii=False)}")
                
                # 检查是否有不同的响应
                message = response_data.get('message', '')
                if "接口【】名字错误" not in message:
                    print(f"🎯 发现不同的响应！")
                    
            except:
                print("无法解析为JSON")
                
        except Exception as e:
            print(f"请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_api_endpoint()
