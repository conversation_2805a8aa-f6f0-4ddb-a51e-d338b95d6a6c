"""
旺店通企业版 API客户端
"""
import json
import logging
import time
from typing import Dict, Any, Optional
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from wdt_enterprise_signature import WDTEnterpriseSignature
from config import WDTConfig


class WDTEnterpriseAPIException(Exception):
    """旺店通企业版API异常"""
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(f"API Error {code}: {message}")


class WDTEnterpriseClient:
    """旺店通企业版API客户端"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化客户端
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or WDTConfig()
        self.logger = logging.getLogger(__name__)
        
        # 创建会话
        self.session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置超时
        self.timeout = 30
        
        # API基础URL
        self.base_url = "https://api.wangdian.cn/openapi2/"
        
    def call_api(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用API
        
        Args:
            endpoint: API端点（如 'trade_query.php'）
            params: 业务参数
            
        Returns:
            API响应数据
            
        Raises:
            WDTEnterpriseAPIException: API调用失败
        """
        try:
            # 构建完整的请求参数
            request_params = WDTEnterpriseSignature.build_request_params(
                params=params,
                sid=self.config.sid,
                appkey=self.config.app_key,
                app_secret=self.config.app_secret
            )
            
            self.logger.debug(f"请求参数: {json.dumps(request_params, ensure_ascii=False, indent=2)}")
            
            # 构建完整URL
            url = self.base_url + endpoint
            
            # 发送POST请求
            response = self.session.post(
                url,
                data=request_params,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            response_data = response.json()
            
            self.logger.debug(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查API状态码
            if response_data.get('code') != 0:
                raise WDTEnterpriseAPIException(
                    code=response_data.get('code', -1),
                    message=response_data.get('message', '未知错误')
                )
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise WDTEnterpriseAPIException(-1, f"HTTP请求失败: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            raise WDTEnterpriseAPIException(-1, f"JSON解析失败: {e}")
        except Exception as e:
            self.logger.error(f"API调用失败: {e}")
            raise WDTEnterpriseAPIException(-1, f"API调用失败: {e}")
    
    def query_sales_stockout(self, start_time: str, end_time: str, page_no: int = 0, page_size: int = 100) -> Dict[str, Any]:
        """
        查询销售出库单
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            page_no: 页码（从0开始）
            page_size: 每页大小
            
        Returns:
            查询结果
        """
        params = {
            'start_time': start_time,
            'end_time': end_time,
            'page_no': page_no,
            'page_size': page_size
        }
        
        return self.call_api('stockout_query.php', params)
    
    def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            连接是否成功
        """
        try:
            # 使用一个简单的查询来测试连接
            from datetime import datetime, timedelta
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=1)
            
            self.query_sales_stockout(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                page_size=1
            )
            return True
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
