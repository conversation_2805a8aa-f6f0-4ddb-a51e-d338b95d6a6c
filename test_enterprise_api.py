#!/usr/bin/env python3
"""
旺店通企业版API测试脚本
"""
import logging
from datetime import datetime, timedelta

from wdt_enterprise_client import WDTEnterpriseClient, WDTEnterpriseAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_signature_algorithm():
    """测试签名算法"""
    from wdt_enterprise_signature import WDTEnterpriseSignature
    
    print("=== 测试签名算法 ===")
    
    # 使用文档中的示例数据
    params = {
        'appkey': 'test2-xx',
        'page_no': '0',
        'end_time': '2016-08-01 13:00:00',
        'start_time': '2016-08-01 12:00:00',
        'page_size': '40',
        'sid': 'test2',
        'timestamp': '1470042310'
    }
    
    app_secret = '12345'
    
    # 构建签名字符串
    sign_string = WDTEnterpriseSignature.build_sign_string(params)
    print(f"签名字符串: {sign_string}")
    
    # 生成签名
    sign = WDTEnterpriseSignature.generate_sign(params, app_secret)
    print(f"生成的签名: {sign}")
    print(f"期望的签名: ad4e6fe037ea6e3ba4768317be9d1309")
    print(f"签名匹配: {sign == 'ad4e6fe037ea6e3ba4768317be9d1309'}")
    print()


def test_api_connection():
    """测试API连接"""
    print("=== 测试API连接 ===")
    
    try:
        # 验证配置
        config = WDTConfig
        print("✓ 配置验证通过")
        print(f"  SID: {config.SID}")
        print(f"  APP_KEY: {config.APP_KEY}")
        print()

        # 创建客户端
        client = WDTEnterpriseClient(config)
        print("✓ 客户端创建成功")
        print()
        
        # 测试查询功能
        print("=== 测试查询功能 ===")
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=30)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            response = client.query_sales_stockout(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                page_size=5
            )
            print("✓ API查询成功")
            print(f"响应数据: {response}")
            
        except WDTEnterpriseAPIException as e:
            print(f"✗ API查询失败: {e}")
            return False
        
        print()
        print("=== 测试完成 ===")
        print("✓ 所有测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("旺店通企业版API测试")
    print("=" * 50)
    
    setup_logging()
    
    # 测试签名算法
    test_signature_algorithm()
    
    # 测试API连接
    success = test_api_connection()
    
    print("=" * 50)
    if success:
        print("✓ 测试完成，API连接正常")
        print()
        print("使用说明:")
        print("1. 企业版API使用不同的签名算法")
        print("2. API端点为: https://api.wangdian.cn/openapi2/")
        print("3. 查看API文档: https://open.wangdian.cn/qyb/open/apidoc")
    else:
        print("⚠️ 测试失败，请检查配置和网络连接")


if __name__ == "__main__":
    main()
