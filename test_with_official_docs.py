#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
根据官方文档测试 sales.trade.query 接口
"""

import json
from datetime import datetime, timedelta
from wdt_original_client import WDTOriginalClient
from config import WDTConfig

def test_with_official_docs():
    """根据官方文档测试接口"""
    
    print("=== 根据官方文档测试 sales.trade.query 接口 ===\n")
    
    client = WDTOriginalClient(enable_rate_limit=False)
    
    # 根据文档设置时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)  # 扩大到7天，增加命中数据的可能性
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"⏰ 测试时间范围: {start_time_str} ~ {end_time_str}")
    
    # 根据官方文档的参数要求进行测试
    test_cases = [
        {
            'name': '测试1: 仅必需参数（状态30-待审核）',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30  # 必需参数：待审核
            }
        },
        {
            'name': '测试2: 仅必需参数（状态5-已取消）',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 5   # 必需参数：已取消
            }
        },
        {
            'name': '测试3: 添加分页参数',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '测试4: 添加货主编号（空值）',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'owner_no': '',
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '测试5: 添加仓库编号（空值）',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'warehouse_no': '',
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '测试6: 添加存在标记参数',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'is_exist_flag': 0,  # 0 全部
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '测试7: 完整参数（所有可选参数）',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'owner_no': '',
                'warehouse_no': '',
                'is_exist_flag': 0,
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '测试8: 较小的分页大小',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'page_no': 0,
                'page_size': 5
            }
        },
        {
            'name': '测试9: 更短的时间范围（1天）',
            'params': {
                'start_time': (end_time - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_time_str,
                'trade_status': 30,
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '测试10: 存在标记=1',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'is_exist_flag': 1,  # 1存在标记
                'page_no': 0,
                'page_size': 10
            }
        }
    ]
    
    successful_tests = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"🧪 {test_case['name']}")
        print(f"{'='*60}")
        
        try:
            params = test_case['params']
            print(f"📝 参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
            
            # 先尝试不压缩
            response = client.call_api('sales.trade.query', params, compress_response=False)
            
            flag = response.get('flag')
            code = response.get('code')
            message = response.get('message', '')
            
            print(f"📊 响应: {flag} - {code} - {message}")
            
            if flag == 'success':
                print(f"🎉 成功！接口调用正常！")
                successful_tests.append(test_case)
                
                content = response.get('content', {})
                if isinstance(content, dict):
                    total = content.get('total', 0)
                    trades = content.get('content', [])  # 根据文档，数据在content字段中
                    print(f"📈 数据统计: 总计 {total} 条，返回 {len(trades)} 条")
                    
                    if trades:
                        print(f"📋 订单详情示例:")
                        for j, trade in enumerate(trades[:2]):
                            print(f"   订单 {j+1}:")
                            print(f"     ERP单号: {trade.get('trade_no', 'N/A')}")
                            print(f"     原始单号: {trade.get('src_tids', 'N/A')}")
                            print(f"     仓储单号: {trade.get('src_order_no', 'N/A')}")
                            print(f"     订单状态: {trade.get('trade_status', 'N/A')}")
                            print(f"     货主编号: {trade.get('owner_no', 'N/A')}")
                            print(f"     仓库编号: {trade.get('warehouse_no', 'N/A')}")
                            print(f"     接单时间: {trade.get('trade_create_time', 'N/A')}")
                            print(f"     货品数量: {trade.get('goods_count', 'N/A')}")
                            print(f"     货品种类: {trade.get('goods_type_count', 'N/A')}")
                            
                            goods_detail = trade.get('goods_detail', [])
                            if goods_detail:
                                print(f"     商品明细: {len(goods_detail)} 种商品")
                                for k, goods in enumerate(goods_detail[:2]):
                                    print(f"       商品 {k+1}: {goods.get('spec_no', 'N/A')} x {goods.get('num', 0)}")
                    else:
                        print(f"📝 当前时间范围内无订单数据")
                        
                # 如果成功，也尝试压缩版本
                print(f"\n🔄 尝试压缩响应版本...")
                try:
                    compressed_response = client.call_api('sales.trade.query', params, compress_response=True)
                    if compressed_response.get('flag') == 'success':
                        print(f"✅ 压缩响应也成功！")
                    else:
                        print(f"❌ 压缩响应失败: {compressed_response.get('message', '')}")
                except Exception as e:
                    print(f"❌ 压缩响应异常: {e}")
                        
            elif code != 'client.invalid-json':
                print(f"🔍 不同的错误类型: {code} - {message}")
                if 'permission' in message.lower() or '权限' in message:
                    print(f"🔑 权限问题")
                elif 'parameter' in message.lower() or '参数' in message:
                    print(f"📝 参数问题")
            else:
                print(f"❌ 仍然是 Invalid json 错误")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    # 总结
    print(f"\n{'='*70}")
    print(f"📊 官方文档测试总结")
    print(f"{'='*70}")
    
    if successful_tests:
        print(f"🎉 成功！发现 {len(successful_tests)} 个有效的测试案例:")
        for test_case in successful_tests:
            print(f"   ✅ {test_case['name']}")
        
        print(f"\n🚀 接口调用成功，可以开始正式使用！")
        print(f"📋 推荐使用的参数组合:")
        best_test = successful_tests[0]
        print(f"   {json.dumps(best_test['params'], ensure_ascii=False, indent=4)}")
        
    else:
        print(f"❌ 所有测试仍然失败")
        print(f"\n🔍 可能的原因:")
        print(f"   1. API文档版本与实际接口不匹配")
        print(f"   2. 需要特定的货主编号或仓库编号")
        print(f"   3. 账号配置可能还有问题")
        print(f"   4. 时间范围内确实没有数据")
        
        print(f"\n💡 建议:")
        print(f"   1. 确认账号是否有测试数据")
        print(f"   2. 尝试提供具体的货主编号和仓库编号")
        print(f"   3. 联系技术支持确认账号配置")

    # 最后尝试：使用示例中的具体值
    print(f"\n{'='*60}")
    print(f"🔍 最后尝试：使用文档示例中的具体值")
    print(f"{'='*60}")

    # 根据文档示例，尝试使用具体的货主和仓库编号
    final_test_cases = [
        {
            'name': '使用示例货主编号',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'owner_no': 'll',  # 从示例响应中看到的货主编号
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '使用示例仓库编号',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'warehouse_no': 'XY-JR-001',  # 从示例响应中看到的仓库编号
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '使用示例货主和仓库编号',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'owner_no': 'll',
                'warehouse_no': 'XY-JR-001',
                'page_no': 0,
                'page_size': 10
            }
        },
        {
            'name': '使用changhe作为货主编号',
            'params': {
                'start_time': start_time_str,
                'end_time': end_time_str,
                'trade_status': 30,
                'owner_no': 'changhe',  # 使用SID作为货主编号
                'page_no': 0,
                'page_size': 10
            }
        }
    ]

    final_success = False

    for test_case in final_test_cases:
        print(f"\n🧪 {test_case['name']}:")
        try:
            params = test_case['params']
            print(f"   参数: {json.dumps(params, ensure_ascii=False)}")

            response = client.call_api('sales.trade.query', params, compress_response=False)

            flag = response.get('flag')
            code = response.get('code')
            message = response.get('message', '')

            print(f"   响应: {flag} - {code} - {message}")

            if flag == 'success':
                print(f"   🎉 成功！找到了正确的参数组合！")
                final_success = True
                break
            elif code != 'client.invalid-json':
                print(f"   🔍 不同的错误，可能有进展！")

        except Exception as e:
            print(f"   ❌ 失败: {e}")

    if not final_success:
        print(f"\n❌ 所有尝试都失败了")
        print(f"\n🎯 最终结论:")
        print(f"   虽然接口权限已开通，但'client.invalid-json'错误持续出现。")
        print(f"   这可能表示:")
        print(f"   1. API文档与实际实现不匹配")
        print(f"   2. 需要特定的账号配置或数据初始化")
        print(f"   3. 可能是旺店通服务端的问题")
        print(f"   ")
        print(f"   🔥 强烈建议联系旺店通技术支持，提供:")
        print(f"   - 账号: {WDTConfig.SID}")
        print(f"   - 错误: client.invalid-json")
        print(f"   - 说明: 按照官方文档所有参数组合都返回此错误")

    print(f"\n=== 官方文档测试完成 ===")

if __name__ == "__main__":
    test_with_official_docs()
