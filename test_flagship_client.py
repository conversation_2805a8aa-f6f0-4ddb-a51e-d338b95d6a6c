#!/usr/bin/env python3
"""
测试旗舰版API客户端
"""
import logging
from datetime import datetime, timedelta

from wdt_flagship_client import WDTFlagshipClient, WDTFlagshipAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_flagship_client():
    """测试旗舰版客户端"""
    print("旺店通旗舰版API客户端测试")
    print("=" * 60)
    
    try:
        # 创建客户端
        client = WDTFlagshipClient()
        print("✓ 旗舰版客户端创建成功")
        print(f"  API端点: {client.base_url}")
        print(f"  SID: {client.config.SID}")
        print(f"  APP_KEY: {client.config.APP_KEY}")
        print()
        
        # 测试连接
        print("=== 测试API连接 ===")
        if client.test_connection():
            print("✓ API连接测试成功")
        else:
            print("✗ API连接测试失败")
            return False
        
        print()
        
        # 测试销售订单查询
        print("=== 测试销售订单查询 ===")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,
                page_size=5
            )
            
            print("✓ 销售订单查询成功")
            
            # 解析响应
            data = response.get('data', {})
            total_count = data.get('total_count', 0)
            detail_list = data.get('detail_list', [])
            
            print(f"  总记录数: {total_count}")
            print(f"  当前页记录数: {len(detail_list)}")
            
            # 显示订单详情
            if detail_list:
                print("\n订单详情:")
                for i, trade in enumerate(detail_list[:3]):  # 只显示前3条
                    print(f"  订单 {i+1}:")
                    print(f"    订单号: {trade.get('trade_no', 'N/A')}")
                    print(f"    状态: {trade.get('trade_status', 'N/A')}")
                    print(f"    创建时间: {trade.get('created', 'N/A')}")
                    print(f"    修改时间: {trade.get('modified', 'N/A')}")
                    print()
            else:
                print("  未找到订单记录")
            
            return True
            
        except WDTFlagshipAPIException as e:
            print(f"✗ 销售订单查询失败: {e}")
            
            # 如果是认证问题，尝试其他接口
            if "卖家帐号" in str(e) or "权限" in str(e):
                print("\n🔍 检测到认证问题，尝试其他测试...")
                return test_other_interfaces(client)
            
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_other_interfaces(client: WDTFlagshipClient):
    """测试其他接口"""
    print("=== 测试其他接口 ===")
    
    # 测试不同的接口
    test_interfaces = [
        {
            'method': 'finance.settle.Logistics.search',
            'params': {
                'start_time': (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 5
            }
        },
        {
            'method': 'wms.stockout.sales.query',
            'params': {
                'start_time': (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 30
            }
        }
    ]
    
    for interface in test_interfaces:
        method = interface['method']
        params = interface['params']
        
        print(f"\n测试接口: {method}")
        
        try:
            response = client.call_api(
                method=method,
                params=params,
                page_size=1,
                page_no=0
            )
            
            print(f"  ✓ 接口 '{method}' 调用成功")
            
            data = response.get('data', {})
            total_count = data.get('total_count', 0)
            print(f"  总记录数: {total_count}")
            
            return True
            
        except WDTFlagshipAPIException as e:
            print(f"  ✗ 接口 '{method}' 调用失败: {e}")
            
            # 如果不是认证问题，可能是接口不存在
            if "卖家帐号" not in str(e) and "权限" not in str(e):
                print(f"    (可能是接口不存在或参数错误)")
    
    return False


def test_simple_request():
    """测试最简单的请求"""
    print("\n=== 测试最简单的请求 ===")
    
    client = WDTFlagshipClient()
    
    # 尝试最简单的参数
    try:
        response = client.call_api(
            method='finance.settle.Logistics.search',
            params={},
            page_size=1,
            page_no=0
        )
        
        print("✓ 简单请求成功")
        return True
        
    except WDTFlagshipAPIException as e:
        print(f"✗ 简单请求失败: {e}")
        
        # 分析错误类型
        if "卖家帐号" in str(e):
            print("  → 认证问题：卖家账号参数有误")
        elif "权限" in str(e):
            print("  → 权限问题：可能需要申请接口权限")
        elif "参数" in str(e):
            print("  → 参数问题：接口参数格式有误")
        else:
            print(f"  → 其他问题: {e}")
        
        return False


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通旗舰版API客户端...")
    print()
    
    # 基础测试
    success = test_flagship_client()
    
    if not success:
        # 如果基础测试失败，尝试简单请求
        test_simple_request()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
