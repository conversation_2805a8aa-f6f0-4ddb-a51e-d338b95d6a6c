#!/usr/bin/env python3
"""
旺店通原始API客户端
基于官方文档的标准API格式
"""
import hashlib
import json
import logging
import requests
import time
import base64
import zlib
from typing import Dict, Any, Optional

from config import WDTConfig


class WDTOriginalAPIException(Exception):
    """旺店通原始API异常"""
    
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(f"API Error {code}: {message}" if code else message)


class WDTOriginalSignature:
    """旺店通原始API签名算法"""
    
    @staticmethod
    def generate_sign(params: Dict[str, Any], app_secret: str) -> str:
        """
        生成MD5签名
        
        Args:
            params: 请求参数（不包含sign字段）
            app_secret: 应用密钥
            
        Returns:
            MD5签名字符串
        """
        # 1. 参数排序
        sorted_params = sorted(params.items())
        
        # 2. 拼接参数字符串
        param_str = ""
        for key, value in sorted_params:
            if value is not None and value != "":
                param_str += f"{key}{value}"
        
        # 3. 添加密钥
        sign_str = app_secret + param_str + app_secret
        
        # 4. MD5加密
        md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        
        return md5_hash.upper()
    
    @staticmethod
    def build_request_params(
        method: str,
        params: Dict[str, Any],
        sid: str,
        appkey: str,
        app_secret: str,
        compress_response_body: bool = True
    ) -> Dict[str, Any]:
        """
        构建完整的请求参数
        
        Args:
            method: 接口方法名
            params: 业务参数
            sid: 卖家账号
            appkey: 应用公钥
            app_secret: 应用私钥
            compress_response_body: 是否压缩响应
            
        Returns:
            完整的请求参数
        """
        # 构建基础参数
        request_params = {
            'sid': sid,
            'appkey': appkey,
            'sign_method': 'md5',
            'format': 'json',
            'timestamp': str(int(time.time())),
            'method': method
        }
        
        # 添加压缩参数
        if compress_response_body:
            request_params['compress_response_body'] = '1'
        
        # 添加业务参数
        request_params.update(params)
        
        # 生成签名
        sign = WDTOriginalSignature.generate_sign(request_params, app_secret)
        request_params['sign'] = sign
        
        return request_params


class WDTOriginalClient:
    """旺店通原始API客户端"""
    
    def __init__(self, config: Optional[WDTConfig] = None):
        """
        初始化客户端
        
        Args:
            config: 配置类，如果为None则使用默认配置
        """
        self.config = config or WDTConfig
        self.logger = logging.getLogger(__name__)
        
        # 创建session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WDT-Original-Client/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        # 设置超时
        self.timeout = 30
        
        # API基础URL
        self.base_url = "https://openapi.wdtwms.com/open_api/service.php"
        
    def call_api(self, method: str, params: Dict[str, Any], compress_response: bool = True) -> Dict[str, Any]:
        """
        调用API
        
        Args:
            method: 接口方法名（如 'sales.trade.query'）
            params: 业务参数
            compress_response: 是否压缩响应
            
        Returns:
            API响应数据
            
        Raises:
            WDTOriginalAPIException: API调用失败
        """
        try:
            # 构建完整的请求参数
            request_params = WDTOriginalSignature.build_request_params(
                method=method,
                params=params,
                sid=self.config.SID,
                appkey=self.config.APP_KEY,
                app_secret=self.config.APP_SECRET,
                compress_response_body=compress_response
            )
            
            # 记录请求参数（隐藏敏感信息）
            log_params = request_params.copy()
            log_params['sign'] = '***'
            self.logger.debug(f"请求参数: {json.dumps(log_params, ensure_ascii=False, indent=2)}")
            
            # 发送GET请求（POST请求无法正确传递method参数）
            response = self.session.get(
                self.base_url,
                params=request_params,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应
            try:
                response_text = response.text

                # 检查是否是压缩响应
                if compress_response and not response_text.startswith('{'):
                    # 尝试解压缩
                    try:
                        # Base64解码
                        compressed_data = base64.b64decode(response_text)
                        # zlib解压缩
                        decompressed_data = zlib.decompress(compressed_data)
                        response_text = decompressed_data.decode('utf-8')
                        self.logger.debug(f"解压缩后的响应: {response_text}")
                    except Exception as decompress_error:
                        self.logger.error(f"解压缩失败: {decompress_error}")
                        self.logger.error(f"原始响应: {response_text[:1000]}")
                        raise WDTOriginalAPIException(f"响应解压缩失败: {decompress_error}")

                response_data = json.loads(response_text)
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败，原始响应: {response_text[:1000]}")
                raise WDTOriginalAPIException(f"响应JSON解析失败: {e}")
            
            self.logger.debug(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查业务状态
            if response_data.get('flag') != 'success':
                error_code = response_data.get('code', 'unknown')
                error_message = response_data.get('message', '未知错误')
                raise WDTOriginalAPIException(error_message, error_code)
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise WDTOriginalAPIException(f"HTTP请求失败: {e}")
        except Exception as e:
            if isinstance(e, WDTOriginalAPIException):
                raise
            self.logger.error(f"API调用失败: {e}")
            raise WDTOriginalAPIException(f"API调用失败: {e}")
    
    def query_sales_trades(
        self,
        start_time: str,
        end_time: str,
        trade_status: int,
        page_no: int = 0,
        page_size: int = 10,
        owner_no: Optional[str] = None,
        warehouse_no: Optional[str] = None,
        is_exist_flag: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        查询销售订单（基于官方文档）

        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS) - 销售订单接单时间
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS) - 销售订单接单时间
            trade_status: 订单状态（必须）- 5:已取消, 30:待审核
            page_no: 页码（从0开始，默认0）
            page_size: 每页大小（默认10，最大100）
            owner_no: 货主编号（可选）
            warehouse_no: 仓库编号（可选）
            is_exist_flag: 标记过滤（可选）- 0:全部, 1:存在标记, 2:不存在标记

        Returns:
            查询结果，包含content数组和total总数

        Note:
            - 时间跨度最大1天
            - 强制启用压缩（compress_response_body=1）
            - 建议减少并发量以降低服务器压力
        """
        # 验证必须参数
        if not trade_status:
            raise ValueError("trade_status是必须参数，5:已取消, 30:待审核")

        # 构建参数
        params = {
            'start_time': start_time,
            'end_time': end_time,
            'trade_status': trade_status,
            'page_no': page_no,
            'page_size': min(page_size, 100)  # 限制最大100
        }

        # 添加可选参数
        if owner_no:
            params['owner_no'] = owner_no
        if warehouse_no:
            params['warehouse_no'] = warehouse_no
        if is_exist_flag is not None:
            params['is_exist_flag'] = is_exist_flag

        # 强制启用压缩
        return self.call_api('sales.trade.query', params, compress_response=True)
    
    def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            连接是否成功
        """
        try:
            # 使用一个简单的查询来测试连接
            from datetime import datetime, timedelta
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=1)
            
            self.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                page_size=1
            )
            return True
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
