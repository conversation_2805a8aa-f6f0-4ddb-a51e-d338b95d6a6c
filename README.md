# 旺店通WMS销售出库单查询工具

这是一个用于查询旺店通WMS系统销售出库单的Python工具包。

## 功能特性

- 🔍 支持多种查询条件的销售出库单查询
- 📊 自动处理分页和时间范围分割
- 💾 支持导出为Excel、CSV、JSON格式
- 📈 提供数据统计分析功能
- 🔐 完整的API签名验证机制
- 🛡️ 错误处理和重试机制
- 📝 详细的日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制配置文件模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的旺店通API配置：
```env
WDT_SID=your_sid
WDT_APP_KEY=your_app_key
WDT_APP_SECRET=your_app_secret
WDT_API_URL=https://openapi.wdtwms.com/open_api/service.php
```

## 快速开始

### 基础查询

```python
from sales_stockout import SalesStockoutQuery
from datetime import datetime, timedelta

# 创建查询器
query = SalesStockoutQuery()

# 查询最近1小时的销售出库单
end_time = datetime.now()
start_time = end_time - timedelta(hours=1)

response = query.query_sales_stockout(
    start_time=start_time,
    end_time=end_time,
    status_type=0,  # 延时发货&已完成
    page_size=100
)

orders = response.get('data', {}).get('order', [])
print(f"查询到 {len(orders)} 条出库单")
```

### 日期范围查询

```python
# 查询昨天的所有销售出库单
from datetime import date, timedelta

yesterday = date.today() - timedelta(days=1)
today = date.today()

orders = query.query_sales_stockout_by_date_range(
    start_date=yesterday,
    end_date=today,
    status_type=0
)
```

### 数据导出

```python
from data_processor import SalesStockoutProcessor

# 创建数据处理器
processor = SalesStockoutProcessor()

# 导出到Excel
excel_file = processor.export_to_excel(orders)

# 导出到CSV
csv_file = processor.export_to_csv(orders)

# 获取统计信息
stats = processor.get_summary_statistics(orders)
```

## API参数说明

### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_time | str/datetime | 是 | 开始时间 |
| end_time | str/datetime | 是 | 结束时间 |
| status_type | int | 否 | 出库单状态类型 |
| warehouse_no | str | 否 | 仓库编号 |
| stockout_no | str | 否 | 出库单号 |
| shop_nos | str | 否 | 店铺编号（多个用逗号分隔） |
| src_order_no | str | 否 | 系统订单号 |
| logistics_no | str | 否 | 物流单号 |

### 状态类型说明

- `0`: 延时发货&已完成
- `1`: 已取消
- `2`: 待分配~延时发货
- `3`: 按指定status查询

### 出库单状态码

| 状态码 | 状态名称 |
|--------|----------|
| 5 | 已取消 |
| 50 | 待审核 |
| 51 | 缺货 |
| 60 | 待分配 |
| 70 | 待发货 |
| 75 | 待拣货 |
| 90 | 延时发货 |
| 110 | 已完成 |

## 文件结构

```
├── config.py              # 配置文件
├── wdt_signature.py       # API签名算法
├── wdt_client.py          # API客户端
├── sales_stockout.py      # 销售出库单查询
├── data_processor.py      # 数据处理和导出
├── example.py             # 使用示例
├── requirements.txt       # 依赖包
├── .env.example          # 配置模板
└── README.md             # 说明文档
```

## 注意事项

1. **时间限制**: API要求start_time和end_time最大跨度为60分钟
2. **频率限制**: 请注意API调用频率限制，避免过于频繁的请求
3. **数据权限**: 接口只返回自有平台及线下平台的隐私数据
4. **分页查询**: 建议单次查询的page_size不超过200

## 错误处理

工具包含完整的错误处理机制：

- API错误会抛出 `WDTAPIException` 异常
- 网络错误会自动重试
- 详细的错误日志记录

## 示例运行

```bash
python example.py
```

## 许可证

MIT License

## 支持

如有问题，请查看旺店通官方API文档或联系技术支持。
