#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试不同的method参数名称
"""

import json
import requests
import time
import hashlib
from config import WDTConfig

def generate_sign(params, app_secret):
    """生成签名"""
    # 排序参数
    sorted_params = sorted(params.items())
    
    # 构建签名字符串
    param_str = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_str = f"{app_secret}{param_str}{app_secret}"
    
    # 生成MD5签名
    md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    return md5_hash.upper()

def test_method_param_names():
    """测试不同的method参数名称"""
    
    print("=== 测试不同的method参数名称 ===\n")
    
    # 基础参数
    base_params = {
        'sid': WDTConfig.SID,
        'appkey': WDTConfig.APP_KEY,
        'sign_method': 'md5',
        'format': 'json',
        'timestamp': str(int(time.time())),
        'compress_response_body': '1',
        'start_time': '2025-07-04 11:00:00',
        'end_time': '2025-07-04 12:00:00',
        'trade_status': 30
    }
    
    # 测试不同的method参数名称
    method_param_names = [
        ('method', 'sales.trade.query'),
        ('api_method', 'sales.trade.query'),
        ('interface', 'sales.trade.query'),
        ('api_name', 'sales.trade.query'),
        ('service', 'sales.trade.query'),
        ('action', 'sales.trade.query'),
        ('api', 'sales.trade.query'),
        ('func', 'sales.trade.query'),
        ('operation', 'sales.trade.query'),
        ('cmd', 'sales.trade.query'),
    ]
    
    for param_name, param_value in method_param_names:
        print(f"\n--- 测试参数名: {param_name} ---")
        
        # 构建请求参数
        request_params = base_params.copy()
        request_params[param_name] = param_value
        
        # 生成签名
        sign = generate_sign(request_params, WDTConfig.APP_SECRET)
        request_params['sign'] = sign
        
        print(f"参数: {param_name}={param_value}")
        
        try:
            response = requests.post(
                "https://openapi.wdtwms.com/open_api/service.php",
                data=request_params,
                timeout=30
            )
            
            response_data = response.json()
            print(f"响应: {json.dumps(response_data, ensure_ascii=False)}")
            
            # 如果不是接口名字错误，说明找到了正确的参数名
            if "接口【】名字错误" not in response_data.get('message', ''):
                print(f"🎯 可能找到了正确的参数名: {param_name}")
                
        except Exception as e:
            print(f"请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_method_param_names()
