#!/usr/bin/env python3
"""
测试不同的API端点和接口组合
"""
import logging
from datetime import datetime, timedelta

from wdt_original_client import WDTOriginalClient, WDTOriginalAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_different_endpoints():
    """测试不同的API端点"""
    print("测试不同的API端点")
    print("=" * 50)
    
    # 可能的API端点
    endpoints = [
        ("原始API（当前）", "https://openapi.wdtwms.com/open_api/service.php"),
        ("测试环境", "http://123.56.134.23/open_api/service.php"),
        ("企业版API", "https://api.wangdian.cn/openapi2/"),
        ("可能的变体1", "https://openapi.wdtwms.com/api/service.php"),
        ("可能的变体2", "https://api.wdtwms.com/open_api/service.php"),
        ("可能的变体3", "https://openapi.wangdian.cn/open_api/service.php")
    ]
    
    # 准备测试参数
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    test_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'trade_status': 30,
        'page_no': 0,
        'page_size': 1
    }
    
    for name, endpoint in endpoints:
        print(f"\n=== 测试端点: {name} ===")
        print(f"URL: {endpoint}")
        
        try:
            # 创建临时客户端
            client = WDTOriginalClient()
            client.base_url = endpoint
            
            # 测试连接
            response = client.call_api('sales.trade.query', test_params)
            
            print("✓ 连接成功")
            print(f"  响应状态: {response.get('flag')}")
            
            if response.get('flag') == 'success':
                print("✓ 接口调用成功！")
                content = response.get('content', [])
                total = response.get('total', 0)
                print(f"  总记录数: {total}")
                return endpoint  # 返回成功的端点
            else:
                error_code = response.get('code')
                error_message = response.get('message')
                print(f"  业务错误: {error_code} - {error_message}")
                
                if "权限" in error_message:
                    print("  → 端点可用，但需要权限")
                    return endpoint
                
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            
            # 分析错误类型
            if "404" in str(e):
                print("  → 端点不存在")
            elif "timeout" in str(e).lower():
                print("  → 连接超时")
            elif "ssl" in str(e).lower():
                print("  → SSL证书问题")
            else:
                print(f"  → 其他错误: {type(e).__name__}")
    
    return None


def test_interface_variations():
    """测试接口名称的不同变体"""
    print("\n=== 测试接口名称变体 ===")
    
    # 基于文档的接口名称变体
    interface_variations = [
        "sales.trade.query",      # 官方文档
        "sales_trade_query",      # 下划线格式
        "salesTradeQuery",        # 驼峰格式
        "SalesTradeQuery",        # 大驼峰格式
        "sales-trade-query",      # 连字符格式
        "SALES.TRADE.QUERY",      # 大写格式
        "wms.sales.trade.query",  # 带前缀
        "api.sales.trade.query",  # 带API前缀
    ]
    
    client = WDTOriginalClient()
    
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    test_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'trade_status': 30,
        'page_no': 0,
        'page_size': 1
    }
    
    for interface in interface_variations:
        print(f"\n测试接口: {interface}")
        
        try:
            response = client.call_api(interface, test_params)
            
            print("✓ 调用成功")
            print(f"  响应状态: {response.get('flag')}")
            
            if response.get('flag') == 'success':
                print("✓ 接口可用！")
                return interface
            else:
                error_message = response.get('message', '')
                print(f"  错误: {error_message}")
                
                if "权限" in error_message:
                    print("  → 接口存在但需要权限")
                    return interface
                
        except Exception as e:
            print(f"✗ 失败: {e}")
    
    return None


def test_minimal_request():
    """测试最小化请求参数"""
    print("\n=== 测试最小化请求参数 ===")
    
    client = WDTOriginalClient()
    
    # 只使用必须参数
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    minimal_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'trade_status': 30
    }
    
    print("使用最小参数集:")
    print(f"  start_time: {minimal_params['start_time']}")
    print(f"  end_time: {minimal_params['end_time']}")
    print(f"  trade_status: {minimal_params['trade_status']}")
    
    try:
        response = client.call_api('sales.trade.query', minimal_params)
        
        print("✓ 最小参数调用成功")
        print(f"  响应状态: {response.get('flag')}")
        
        if response.get('flag') == 'success':
            print("✓ 接口可用！")
            return True
        else:
            error_message = response.get('message', '')
            print(f"  错误: {error_message}")
            
            if "权限" in error_message:
                print("  → 接口存在但需要权限")
                return True
                
    except Exception as e:
        print(f"✗ 失败: {e}")
    
    return False


def check_api_documentation():
    """检查API文档一致性"""
    print("\n=== API文档一致性检查 ===")
    
    print("根据您提供的文档:")
    print("- 接口名称: sales.trade.query")
    print("- 必须参数: start_time, end_time, trade_status")
    print("- 可选参数: owner_no, warehouse_no, is_exist_flag, page_no, page_size")
    print("- 压缩要求: compress_response_body=1")
    print("- 时间限制: 最大1天跨度")
    print()
    
    print("可能的问题:")
    print("1. 文档对应的API端点与当前使用的不同")
    print("2. 需要特定的API版本或认证方式")
    print("3. 接口名称在实际实现中有所不同")
    print("4. 账号没有该接口的访问权限")
    print()
    
    print("建议:")
    print("1. 确认文档对应的具体API端点")
    print("2. 检查账号是否有sales.trade.query接口权限")
    print("3. 联系旺店通技术支持确认接口可用性")


def main():
    """主函数"""
    setup_logging()
    
    print("开始全面测试API端点和接口...")
    print()
    
    # 测试不同端点
    working_endpoint = test_different_endpoints()
    
    if working_endpoint:
        print(f"\n🎯 找到可用端点: {working_endpoint}")
    else:
        print("\n❌ 未找到可用的API端点")
    
    # 测试接口名称变体
    working_interface = test_interface_variations()
    
    if working_interface:
        print(f"\n🎯 找到可用接口: {working_interface}")
    else:
        print("\n❌ 未找到可用的接口名称")
    
    # 测试最小参数
    minimal_success = test_minimal_request()
    
    if minimal_success:
        print("\n✓ 最小参数测试成功")
    else:
        print("\n❌ 最小参数测试失败")
    
    # 文档一致性检查
    check_api_documentation()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
