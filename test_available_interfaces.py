#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试可用的接口
"""

import json
from datetime import datetime, timedelta
from wdt_original_client import WDTOriginalClient

def test_available_interfaces():
    """测试可用的接口"""
    
    print("=== 测试可用的接口 ===\n")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 创建客户端
    client = WDTOriginalClient()
    
    # 测试各种可能的接口
    test_interfaces = [
        # 系统相关接口
        ('system.time', {}, '系统时间'),
        ('system.info', {}, '系统信息'),
        ('api.version', {}, 'API版本'),
        
        # 基础查询接口
        ('goods.query', {'page_no': 0, 'page_size': 1}, '商品查询'),
        ('warehouse.query', {'page_no': 0, 'page_size': 1}, '仓库查询'),
        ('stock.query', {'page_no': 0, 'page_size': 1}, '库存查询'),
        
        # 销售相关接口
        ('sales.trade.query', {
            'start_time': '2025-07-05 13:00:00',
            'end_time': '2025-07-05 14:00:00',
            'page_no': 0,
            'page_size': 1
        }, '销售订单查询'),
        
        ('trade.query', {
            'start_time': '2025-07-05 13:00:00', 
            'end_time': '2025-07-05 14:00:00',
            'page_no': 0,
            'page_size': 1
        }, '交易查询'),
        
        # 订单相关接口
        ('order.query', {
            'start_time': '2025-07-05 13:00:00',
            'end_time': '2025-07-05 14:00:00', 
            'page_no': 0,
            'page_size': 1
        }, '订单查询'),
        
        # 出入库相关接口
        ('stockout.query', {
            'start_time': '2025-07-05 13:00:00',
            'end_time': '2025-07-05 14:00:00',
            'page_no': 0,
            'page_size': 1
        }, '出库单查询'),
        
        ('stockin.query', {
            'start_time': '2025-07-05 13:00:00',
            'end_time': '2025-07-05 14:00:00',
            'page_no': 0,
            'page_size': 1
        }, '入库单查询'),
        
        # 用户相关接口
        ('user.info', {}, '用户信息'),
        ('account.info', {}, '账户信息'),
    ]
    
    available_interfaces = []
    permission_needed = []
    not_deployed = []
    
    for interface_name, params, description in test_interfaces:
        print(f"测试接口: {interface_name} ({description})")
        
        try:
            result = client.call_api(interface_name, params)
            
            if result.get('flag') == 'success':
                print(f"   ✅ 成功: {interface_name}")
                available_interfaces.append((interface_name, description))
                
                # 显示部分响应数据
                content = result.get('content', {})
                if isinstance(content, dict):
                    if 'total_count' in content:
                        print(f"      总记录数: {content.get('total_count', 0)}")
                    elif content:
                        print(f"      响应字段: {list(content.keys())}")
                else:
                    print(f"      响应内容: {str(content)[:100]}")
            else:
                print(f"   ❌ 失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ 异常: {error_msg}")
            
            # 分类错误
            if "权限" in error_msg:
                permission_needed.append((interface_name, description))
                print(f"      🔑 需要权限")
            elif "未部署" in error_msg or "暂不支持" in error_msg:
                not_deployed.append((interface_name, description))
                print(f"      🚫 接口未部署")
            else:
                print(f"      ❓ 其他错误")
        
        print()
    
    # 总结报告
    print("=" * 60)
    print("📊 接口测试总结报告")
    print("=" * 60)
    
    print(f"\n✅ 可用接口 ({len(available_interfaces)} 个):")
    for interface_name, description in available_interfaces:
        print(f"   - {interface_name} ({description})")
    
    print(f"\n🔑 需要权限的接口 ({len(permission_needed)} 个):")
    for interface_name, description in permission_needed:
        print(f"   - {interface_name} ({description})")
    
    print(f"\n🚫 未部署的接口 ({len(not_deployed)} 个):")
    for interface_name, description in not_deployed:
        print(f"   - {interface_name} ({description})")
    
    if available_interfaces:
        print(f"\n🎉 发现 {len(available_interfaces)} 个可用接口！")
        print("建议优先使用这些接口进行数据获取。")
    elif permission_needed:
        print(f"\n⏳ 有 {len(permission_needed)} 个接口需要权限，请确认权限申请状态。")
    else:
        print(f"\n⚠️ 暂无可用接口，请检查权限配置。")

if __name__ == "__main__":
    test_available_interfaces()
