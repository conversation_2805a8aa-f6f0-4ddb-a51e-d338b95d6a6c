#!/usr/bin/env python3
"""
旺店通WMS销售订单查询API测试
"""
import logging
import json
from datetime import datetime, timedelta

from wdt_sales_trade_client import WDTSalesTradeClient, WDTSalesTradeAPIException
from sales_trade_query import SalesTradeQuery
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_sales_trade_api():
    """测试销售订单查询API"""
    print("旺店通WMS销售订单查询API测试")
    print("=" * 60)
    
    try:
        # 验证配置
        config = WDTConfig
        print("✓ 配置验证通过")
        print(f"  SID: {config.SID}")
        print(f"  APP_KEY: {config.APP_KEY}")
        print()
        
        # 创建客户端
        client = WDTSalesTradeClient(config)
        print("✓ 客户端创建成功")
        print()
        
        # 测试查询功能
        print("=== 测试查询功能 ===")
        
        # 查询最近1小时的订单
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,  # 待审核
                page_size=5
            )
            
            print("✓ API查询成功")
            print(f"  总记录数: {response.get('total', 0)}")
            print(f"  当前页记录数: {len(response.get('content', []))}")
            
            # 显示订单详情
            content = response.get('content', [])
            if content:
                print("\n订单详情:")
                for i, trade in enumerate(content[:3]):  # 只显示前3条
                    print(f"  订单 {i+1}:")
                    print(f"    订单号: {trade.get('trade_no')}")
                    print(f"    原始单号: {trade.get('src_tids')}")
                    print(f"    状态: {trade.get('trade_status')}")
                    print(f"    金额: {trade.get('total_amount')}")
                    print(f"    商品数量: {trade.get('goods_count')}")
                    print(f"    收货地址: {trade.get('receiver_area')}")
                    print()
            else:
                print("  未找到订单记录")
            
        except WDTSalesTradeAPIException as e:
            print(f"✗ API查询失败: {e}")
            return False
        
        print("=== 测试高级查询功能 ===")
        
        # 创建查询器
        query = SalesTradeQuery(config)
        
        try:
            # 测试分页查询
            all_trades = query.query_all_pages(
                start_time=start_time,
                end_time=end_time,
                trade_status=30,
                page_size=10,
                max_pages=2  # 限制最多查询2页
            )
            
            print(f"✓ 分页查询成功，共 {len(all_trades)} 条记录")
            
            # 获取汇总信息
            summary = query.get_trade_summary(all_trades)
            print(f"  汇总信息:")
            print(f"    总订单数: {summary['total_count']}")
            print(f"    总金额: {summary['total_amount']}")
            print(f"    总商品数: {summary['total_goods_count']}")
            print(f"    状态分布: {summary['status_summary']}")
            
        except Exception as e:
            print(f"✗ 高级查询失败: {e}")
            return False
        
        print("\n=== 测试时间段查询 ===")
        
        try:
            # 测试查询最近6小时（分2段）
            segment_start = end_time - timedelta(hours=6)
            segment_trades = query.query_by_hour_segments(
                start_time=segment_start,
                end_time=end_time,
                segment_hours=3,  # 每段3小时
                trade_status=30,
                page_size=20
            )
            
            print(f"✓ 时间段查询成功，共 {len(segment_trades)} 条记录")
            
        except Exception as e:
            print(f"✗ 时间段查询失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_different_statuses():
    """测试不同状态的订单查询"""
    print("\n=== 测试不同状态查询 ===")
    
    config = WDTConfig
    client = WDTSalesTradeClient(config)
    
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=24)  # 查询最近24小时
    
    statuses = {
        5: "已取消",
        30: "待审核"
    }
    
    for status_code, status_name in statuses.items():
        try:
            print(f"\n查询{status_name}订单 (状态码: {status_code})...")
            
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=status_code,
                page_size=10
            )
            
            total = response.get('total', 0)
            content_count = len(response.get('content', []))
            
            print(f"  ✓ {status_name}订单: 总数 {total}, 当前页 {content_count}")
            
        except WDTSalesTradeAPIException as e:
            print(f"  ✗ {status_name}订单查询失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通WMS销售订单查询API...")
    print()
    
    # 基本功能测试
    success = test_sales_trade_api()
    
    if success:
        # 扩展测试
        test_different_statuses()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
