#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试基础接口，看看是否有任何接口可以工作
"""

import json
import requests
import time
import hashlib
from config import WDTConfig

def generate_sign(params, app_secret):
    """生成签名"""
    # 排序参数
    sorted_params = sorted(params.items())
    
    # 构建签名字符串
    param_str = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_str = f"{app_secret}{param_str}{app_secret}"
    
    # 生成MD5签名
    md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    return md5_hash.upper()

def test_basic_interfaces():
    """测试基础接口"""
    
    print("=== 测试基础接口 ===\n")
    
    # 基础参数
    base_params = {
        'sid': WDTConfig.SID,
        'appkey': WDTConfig.APP_KEY,
        'sign_method': 'md5',
        'format': 'json',
        'timestamp': str(int(time.time()))
    }
    
    # 测试一些基础接口
    test_interfaces = [
        # 系统接口
        'system.time',
        'system.info',
        'api.version',
        'user.info',
        'account.info',
        
        # 简单查询接口
        'warehouse.query',
        'goods.query',
        'stock.query',
        
        # 可能的销售接口
        'trade.query',
        'order.query',
        'sales.query',
        
        # 测试空接口名
        '',
        
        # 测试特殊字符
        'test',
        'ping',
        'hello'
    ]
    
    for interface in test_interfaces:
        print(f"\n--- 测试接口: '{interface}' ---")
        
        # 构建请求参数
        request_params = base_params.copy()
        if interface:  # 只有非空接口名才添加method参数
            request_params['method'] = interface
        
        # 生成签名
        sign = generate_sign(request_params, WDTConfig.APP_SECRET)
        request_params['sign'] = sign
        
        try:
            response = requests.post(
                "https://openapi.wdtwms.com/open_api/service.php",
                data=request_params,
                timeout=30
            )
            
            response_data = response.json()
            message = response_data.get('message', '')
            
            print(f"响应: {json.dumps(response_data, ensure_ascii=False)}")
            
            # 分析响应
            if "接口【】名字错误" not in message:
                print(f"🎯 接口 '{interface}' 返回了不同的响应！")
            
            if "权限" in message or "permission" in message.lower():
                print(f"🔑 接口 '{interface}' 存在但需要权限")
                
            if "参数" in message or "parameter" in message.lower():
                print(f"📝 接口 '{interface}' 存在但参数有问题")
                
        except Exception as e:
            print(f"请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_basic_interfaces()
