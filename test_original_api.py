#!/usr/bin/env python3
"""
测试旺店通原始API（基于官方文档）
"""
import logging
from datetime import datetime, timedelta

from wdt_original_client import WDTOriginalClient, WDTOriginalAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_original_api():
    """测试原始API"""
    print("旺店通原始API测试（基于官方文档）")
    print("=" * 60)
    
    try:
        # 验证配置
        config = WDTConfig
        print("✓ 配置验证通过")
        print(f"  SID: {config.SID}")
        print(f"  APP_KEY: {config.APP_KEY}")
        print()
        
        # 创建客户端
        client = WDTOriginalClient(config)
        print("✓ 原始API客户端创建成功")
        print(f"  API端点: {client.base_url}")
        print()
        
        # 测试销售订单查询
        print("=== 测试销售订单查询 ===")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,  # 待审核
                page_size=5
            )
            
            print("✓ 销售订单查询成功")
            
            # 解析响应
            content = response.get('content', {})
            
            # 检查不同的数据结构
            if isinstance(content, dict):
                if 'order' in content:
                    orders = content['order']
                    total_count = content.get('total', len(orders))
                elif 'list' in content:
                    orders = content['list']
                    total_count = content.get('total', len(orders))
                elif 'trades' in content:
                    orders = content['trades']
                    total_count = content.get('total', len(orders))
                else:
                    orders = content.get('data', [])
                    total_count = content.get('total', len(orders))
            elif isinstance(content, list):
                orders = content
                total_count = len(orders)
            else:
                orders = []
                total_count = 0
            
            print(f"  总记录数: {total_count}")
            print(f"  当前页记录数: {len(orders)}")
            
            # 显示订单详情
            if orders:
                print("\n订单详情:")
                for i, trade in enumerate(orders[:3]):  # 只显示前3条
                    print(f"  订单 {i+1}:")
                    print(f"    订单号: {trade.get('trade_no', trade.get('order_no', 'N/A'))}")
                    print(f"    原始单号: {trade.get('src_tids', trade.get('src_tid', 'N/A'))}")
                    print(f"    状态: {trade.get('trade_status', trade.get('status', 'N/A'))}")
                    print(f"    金额: {trade.get('total_amount', trade.get('amount', 'N/A'))}")
                    print(f"    商品数量: {trade.get('goods_count', trade.get('item_count', 'N/A'))}")
                    print(f"    收货地址: {trade.get('receiver_area', trade.get('address', 'N/A'))}")
                    print(f"    创建时间: {trade.get('created', trade.get('create_time', 'N/A'))}")
                    print()
            else:
                print("  未找到订单记录")
            
            return True
            
        except WDTOriginalAPIException as e:
            print(f"✗ 销售订单查询失败: {e}")
            
            # 分析错误类型
            if "权限" in str(e):
                print("  → 权限问题：需要申请接口权限")
            elif "参数" in str(e):
                print("  → 参数问题：检查参数格式")
            elif "时间" in str(e):
                print("  → 时间问题：检查时间格式或范围")
            elif "接口" in str(e) and "名字" in str(e):
                print("  → 接口名称问题：检查method参数")
            else:
                print(f"  → 其他问题: {e}")
            
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_different_methods():
    """测试不同的接口方法名"""
    print("\n=== 测试不同接口方法名 ===")
    
    client = WDTOriginalClient()
    
    # 测试不同的方法名
    methods_to_try = [
        "sales.trade.query",
        "sales_trade_query", 
        "salesTradeQuery",
        "trade.query",
        "wms.sales.trade.query"
    ]
    
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'page_no': 0,
        'page_size': 1
    }
    
    for method in methods_to_try:
        print(f"\n尝试方法: {method}")
        
        try:
            response = client.call_api(method, params)
            print(f"  ✓ 成功")
            
            content = response.get('content', {})
            if content:
                print(f"  → 返回数据: {type(content)}")
                if isinstance(content, dict) and content:
                    print(f"  → 数据键: {list(content.keys())}")
            
            return True  # 找到可用的方法
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    return False


def test_different_time_ranges():
    """测试不同的时间范围"""
    print("\n=== 测试不同时间范围 ===")
    
    client = WDTOriginalClient()
    
    # 测试不同的时间范围
    time_ranges = [
        ("最近1小时", timedelta(hours=1)),
        ("最近6小时", timedelta(hours=6)),
        ("最近1天", timedelta(days=1)),
        ("最近3天", timedelta(days=3))
    ]
    
    for name, delta in time_ranges:
        print(f"\n测试时间范围: {name}")
        
        end_time = datetime.now()
        start_time = end_time - delta
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                page_size=1
            )
            
            content = response.get('content', {})
            
            # 获取记录数
            if isinstance(content, dict):
                if 'order' in content:
                    count = len(content['order'])
                    total = content.get('total', count)
                elif 'list' in content:
                    count = len(content['list'])
                    total = content.get('total', count)
                else:
                    count = len(content.get('data', []))
                    total = content.get('total', count)
            elif isinstance(content, list):
                count = len(content)
                total = count
            else:
                count = 0
                total = 0
            
            print(f"  ✓ 成功，总记录数: {total}")
            
            if total > 0:
                return True  # 找到有数据的时间范围
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    return False


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通原始API（基于官方文档）...")
    print()
    
    # 基础测试
    success = test_original_api()
    
    if not success:
        print("🔍 尝试不同接口方法名...")
        method_success = test_different_methods()
        
        if not method_success:
            print("🔍 尝试不同时间范围...")
            test_different_time_ranges()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
