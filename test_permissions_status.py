#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试接口权限状态
"""

import json
from datetime import datetime, timedelta
from wdt_original_client import WDTOriginalClient

def test_permissions_status():
    """测试接口权限状态"""
    
    print("=== 测试接口权限状态 ===\n")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建客户端
    client = WDTOriginalClient()
    
    # 设置测试时间范围（最近1小时）
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"查询时间范围: {start_time_str} 到 {end_time_str}\n")
    
    # 测试 sales.trade.query 接口
    print("1. 测试 sales.trade.query 接口:")
    print("-" * 50)
    
    try:
        result = client.query_sales_trades(
            start_time=start_time_str,
            end_time=end_time_str,
            trade_status=30,  # 待审核状态
            page_no=0,
            page_size=10
        )
        
        print("✅ 成功调用 sales.trade.query 接口！")
        print(f"响应结构: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 分析响应数据
        if result.get('flag') == 'success':
            content = result.get('content', {})
            trades = content.get('trades', [])
            total_count = content.get('total_count', 0)
            
            print(f"\n📊 查询结果统计:")
            print(f"   总记录数: {total_count}")
            print(f"   返回记录数: {len(trades)}")
            
            if trades:
                print(f"\n📋 前几条记录预览:")
                for i, trade in enumerate(trades[:3]):
                    print(f"   记录 {i+1}:")
                    print(f"     订单号: {trade.get('trade_no', 'N/A')}")
                    print(f"     状态: {trade.get('trade_status', 'N/A')}")
                    print(f"     创建时间: {trade.get('created', 'N/A')}")
            else:
                print("   📝 当前时间范围内没有数据")
        else:
            print(f"⚠️ 接口调用成功但返回失败: {result}")
            
    except Exception as e:
        error_msg = str(e)
        print(f"❌ 调用失败: {error_msg}")
        
        # 分析错误类型
        if "权限" in error_msg or "permission" in error_msg.lower():
            print("🔑 权限问题 - 可能需要等待权限生效或重新申请")
        elif "参数" in error_msg or "parameter" in error_msg.lower():
            print("📝 参数问题 - 检查参数格式")
        elif "接口" in error_msg and "不存在" in error_msg:
            print("❓ 接口不存在")
        else:
            print("❓ 其他错误")
    
    # 测试其他状态的订单
    print(f"\n2. 测试不同状态的订单:")
    print("-" * 50)
    
    status_map = {
        5: "已取消",
        30: "待审核", 
        0: "全部状态"
    }
    
    for status_code, status_name in status_map.items():
        print(f"\n测试状态 {status_code} ({status_name}):")
        try:
            if status_code == 0:
                # 测试不传状态参数
                result = client.call_api('sales.trade.query', {
                    'start_time': start_time_str,
                    'end_time': end_time_str,
                    'page_no': 0,
                    'page_size': 5
                })
            else:
                result = client.query_sales_trades(
                    start_time=start_time_str,
                    end_time=end_time_str,
                    trade_status=status_code,
                    page_no=0,
                    page_size=5
                )
            
            if result.get('flag') == 'success':
                content = result.get('content', {})
                total_count = content.get('total_count', 0)
                print(f"   ✅ 状态 {status_code}: {total_count} 条记录")
            else:
                print(f"   ❌ 状态 {status_code}: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"   ❌ 状态 {status_code}: {str(e)}")
    
    print(f"\n=== 测试完成 ===")

if __name__ == "__main__":
    test_permissions_status()
