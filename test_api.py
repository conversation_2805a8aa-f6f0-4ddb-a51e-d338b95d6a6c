#!/usr/bin/env python3
"""
API测试脚本
"""
import logging
from datetime import datetime, timedelta

from wdt_client import WDTClient, WDTAPIException
from sales_stockout import SalesStockoutQuery
from data_processor import SalesStockoutProcessor
from config import WDTConfig


def test_config():
    """测试配置"""
    print("=== 测试配置 ===")
    try:
        WDTConfig.validate_config()
        print("✓ 配置验证通过")
        print(f"  SID: {WDTConfig.SID}")
        print(f"  APP_KEY: {WDTConfig.APP_KEY}")
        print(f"  API_URL: {WDTConfig.API_URL}")
        return True
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")
        return False


def test_client():
    """测试客户端创建"""
    print("\n=== 测试客户端 ===")
    try:
        client = WDTClient()
        print("✓ 客户端创建成功")
        return client
    except Exception as e:
        print(f"✗ 客户端创建失败: {e}")
        return None


def test_query():
    """测试查询功能"""
    print("\n=== 测试查询功能 ===")
    try:
        query = SalesStockoutQuery()
        
        # 查询最近30分钟的数据
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=30)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        response = query.query_sales_stockout(
            start_time=start_time,
            end_time=end_time,
            status_type=0,
            page_size=5
        )
        
        data = response.get('data', {})
        orders = data.get('order', [])
        total_count = data.get('total_count', 0)
        
        print(f"✓ 查询成功")
        print(f"  总记录数: {total_count}")
        print(f"  当前页记录数: {len(orders)}")
        
        if orders:
            print("  示例记录:")
            for i, order in enumerate(orders[:3], 1):
                print(f"    {i}. 出库单号: {order.get('order_no')}")
                print(f"       订单号: {order.get('trade_no')}")
                print(f"       状态: {order.get('status')}")
        
        return orders
        
    except WDTAPIException as e:
        print(f"✗ API查询失败: {e}")
        return []
    except Exception as e:
        print(f"✗ 查询失败: {e}")
        return []


def test_data_processor(orders):
    """测试数据处理"""
    print("\n=== 测试数据处理 ===")
    try:
        if not orders:
            print("⚠ 没有数据可供处理")
            return
        
        processor = SalesStockoutProcessor()
        
        # 测试DataFrame转换
        df = processor.to_dataframe(orders)
        print(f"✓ DataFrame转换成功，形状: {df.shape}")
        
        # 测试统计信息
        stats = processor.get_summary_statistics(orders)
        print("✓ 统计信息生成成功")
        print(f"  总出库单数: {stats.get('总出库单数', 0)}")
        
        # 测试JSON导出
        json_file = processor.export_to_json(orders, 'test_output.json')
        print(f"✓ JSON导出成功: {json_file}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理失败: {e}")
        return False


def main():
    """主测试函数"""
    print("旺店通WMS API测试")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 测试配置
    if not test_config():
        print("\n❌ 配置测试失败，请检查 .env 文件")
        return
    
    # 测试客户端
    client = test_client()
    if not client:
        print("\n❌ 客户端测试失败")
        return
    
    # 测试查询
    orders = test_query()
    
    # 测试数据处理
    test_data_processor(orders)
    
    print("\n" + "=" * 50)
    if orders:
        print("🎉 所有测试完成！API可以正常使用。")
    else:
        print("⚠️  测试完成，但没有查询到数据。这可能是正常的（如果时间范围内没有出库单）。")
    
    print("\n使用说明:")
    print("1. 运行 'python main.py --help' 查看命令行工具帮助")
    print("2. 运行 'python example.py' 查看更多使用示例")
    print("3. 查看 'README.md' 和 '使用说明.md' 了解详细用法")


if __name__ == "__main__":
    main()
