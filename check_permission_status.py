#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查权限状态并提供详细信息
"""

import json
import time
from datetime import datetime, timedelta
from wdt_original_client import WDTOriginalClient

def check_permission_status():
    """检查权限状态"""
    
    print("=== 检查接口权限状态 ===\n")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建客户端
    client = WDTOriginalClient()
    
    # 重点测试的接口
    target_interfaces = [
        'sales.trade.query',
        'goods.query', 
        'stock.query',
        'stockin.query'
    ]
    
    print(f"\n🎯 重点检查以下接口的权限状态:")
    for interface in target_interfaces:
        print(f"   - {interface}")
    
    print(f"\n" + "="*60)
    
    for interface_name in target_interfaces:
        print(f"\n🔍 检查接口: {interface_name}")
        print("-" * 40)
        
        # 准备测试参数
        if 'query' in interface_name:
            # 查询类接口需要时间参数
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            params = {
                'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
                'page_no': 0,
                'page_size': 1
            }
        else:
            params = {'page_no': 0, 'page_size': 1}
        
        try:
            print(f"📤 发送请求...")
            result = client.call_api(interface_name, params)
            
            print(f"📥 收到响应:")
            print(f"   Flag: {result.get('flag', 'N/A')}")
            print(f"   Code: {result.get('code', 'N/A')}")
            print(f"   Message: {result.get('message', 'N/A')}")
            
            if result.get('flag') == 'success':
                print(f"   ✅ 权限正常！接口可用！")
                
                # 显示数据统计
                content = result.get('content', {})
                if isinstance(content, dict):
                    if 'total_count' in content:
                        total = content.get('total_count', 0)
                        records = len(content.get('trades', content.get('goods', content.get('stocks', []))))
                        print(f"   📊 数据统计: 总计 {total} 条，返回 {records} 条")
                    
                    # 显示字段信息
                    if content:
                        print(f"   📋 响应字段: {list(content.keys())}")
                
            elif "权限" in result.get('message', ''):
                print(f"   🔑 权限未生效 - 可能需要等待或重新申请")
                
            else:
                print(f"   ❌ 其他错误: {result.get('message', '未知错误')}")
                
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ 请求异常: {error_msg}")
            
            if "权限" in error_msg:
                print(f"   🔑 权限问题 - 请确认权限申请状态")
            elif "参数" in error_msg:
                print(f"   📝 参数问题 - 可能需要调整参数格式")
            else:
                print(f"   ❓ 其他问题 - 需要进一步分析")
    
    print(f"\n" + "="*60)
    print(f"📋 权限检查总结:")
    print(f"   如果显示'权限未生效'，可能的原因:")
    print(f"   1. 权限申请还在审核中")
    print(f"   2. 权限已批准但系统还未同步（通常需要几分钟到几小时）")
    print(f"   3. 需要重新申请或联系技术支持")
    print(f"   4. 账号配置问题（SID、APP_KEY等）")
    
    print(f"\n💡 建议:")
    print(f"   - 如果刚申请权限，请等待10-30分钟后重试")
    print(f"   - 确认在旺店通开放平台上的权限申请状态")
    print(f"   - 检查账号信息是否正确")

if __name__ == "__main__":
    check_permission_status()
