#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试method参数传递问题
"""

import json
import requests
from wdt_original_client import WDTOriginalSignature
from config import WDTConfig

def debug_method_param():
    """调试method参数是否正确传递"""
    
    print("=== 调试method参数传递 ===\n")
    
    # 测试参数
    method = "sales.trade.query"
    params = {
        'start_time': '2025-07-04 11:00:00',
        'end_time': '2025-07-04 12:00:00',
        'trade_status': 30
    }
    
    # 构建请求参数
    request_params = WDTOriginalSignature.build_request_params(
        method=method,
        params=params,
        sid=WDTConfig.SID,
        appkey=WDTConfig.APP_KEY,
        app_secret=WDTConfig.APP_SECRET,
        compress_response_body=True
    )
    
    print("1. 构建的请求参数:")
    log_params = request_params.copy()
    log_params['sign'] = '***'
    print(json.dumps(log_params, ensure_ascii=False, indent=2))
    
    print(f"\n2. method参数值: '{request_params.get('method')}'")
    print(f"3. method参数类型: {type(request_params.get('method'))}")
    print(f"4. method参数长度: {len(request_params.get('method', ''))}")
    
    # 检查所有参数
    print("\n5. 所有参数检查:")
    for key, value in request_params.items():
        if key != 'sign':
            print(f"   {key}: '{value}' (类型: {type(value)})")
    
    # 手动构建POST数据来验证
    print("\n6. 手动构建POST数据:")
    post_data = {}
    for key, value in request_params.items():
        post_data[key] = str(value)
    
    print("POST数据:")
    for key, value in post_data.items():
        if key != 'sign':
            print(f"   {key}={value}")
    
    # 发送请求并查看原始响应
    print("\n7. 发送请求...")
    try:
        response = requests.post(
            "https://openapi.wdtwms.com/open_api/service.php",
            data=post_data,
            timeout=30
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"原始响应内容: {response.text}")
        
        # 尝试解析JSON
        try:
            response_data = response.json()
            print(f"解析后的响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        except:
            print("无法解析为JSON")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    debug_method_param()
