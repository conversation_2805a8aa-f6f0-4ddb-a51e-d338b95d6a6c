#!/usr/bin/env python3
"""
尝试发现可用的接口 - 通过测试已知工作的接口模式
"""
import logging
from datetime import datetime, timedelta

from wdt_original_client import WDTOriginalClient, WDTOriginalAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_known_working_interfaces():
    """测试已知可用的接口，寻找模式"""
    print("测试已知可用的接口模式")
    print("=" * 50)
    
    # 基于之前成功的出库单接口，尝试相关的接口
    known_interfaces = [
        # 出库相关（已知存在）
        "stockout.order.query",
        "stockout_order_query", 
        "stockout.query",
        
        # 入库相关
        "stockin.order.query",
        "stockin_order_query",
        "stockin.query",
        
        # 库存相关
        "stock.query",
        "inventory.query",
        "goods.query",
        
        # 订单相关（可能的变体）
        "order.query",
        "trade.query", 
        "sales.query",
        
        # 基于WMS的命名模式
        "wms.stockout.query",
        "wms.stockin.query", 
        "wms.order.query",
        "wms.trade.query",
        "wms.sales.query",
        
        # 可能的销售相关接口
        "sales.order.query",
        "sales.trade.list",
        "sales.order.list",
        "trade.order.query",
        
        # 其他可能的格式
        "queryStockout",
        "queryTrade",
        "querySales",
        "getSalesOrder",
        "getTradeOrder",
    ]
    
    client = WDTOriginalClient()
    
    # 准备测试参数
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    # 使用最小参数集
    test_params = {
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        'page_no': 0,
        'page_size': 1
    }
    
    working_interfaces = []
    permission_interfaces = []
    param_error_interfaces = []
    
    print(f"测试时间范围: {test_params['start_time']} - {test_params['end_time']}")
    print(f"总共测试 {len(known_interfaces)} 个接口")
    print()
    
    for i, interface in enumerate(known_interfaces, 1):
        print(f"[{i:2d}/{len(known_interfaces)}] 测试接口: {interface}")
        
        try:
            response = client.call_api(interface, test_params)
            
            flag = response.get('flag')
            code = response.get('code')
            message = response.get('message', '')
            
            print(f"  响应: {flag} - {code} - {message}")
            
            if flag == 'success':
                print(f"  ✓ 成功！")
                working_interfaces.append(interface)
            elif "权限" in message:
                print(f"  ⚠ 权限问题")
                permission_interfaces.append(interface)
            elif "参数" in message:
                print(f"  ⚠ 参数问题（接口可能存在）")
                param_error_interfaces.append(interface)
            elif "接口" in message and "名字" in message:
                print(f"  ✗ 接口不存在")
            else:
                print(f"  ❓ 其他错误")
            
        except Exception as e:
            print(f"  ✗ 异常: {e}")
        
        print()
    
    # 输出总结
    print("=" * 50)
    print("接口发现总结:")
    print()
    
    if working_interfaces:
        print(f"✓ 可用接口 ({len(working_interfaces)} 个):")
        for interface in working_interfaces:
            print(f"  - {interface}")
        print()
    
    if permission_interfaces:
        print(f"⚠ 需要权限的接口 ({len(permission_interfaces)} 个):")
        for interface in permission_interfaces:
            print(f"  - {interface}")
        print()
    
    if param_error_interfaces:
        print(f"❓ 参数错误的接口 ({len(param_error_interfaces)} 个):")
        for interface in param_error_interfaces:
            print(f"  - {interface}")
        print()
    
    return working_interfaces + permission_interfaces + param_error_interfaces


def test_sales_specific_interfaces():
    """专门测试销售相关的接口变体"""
    print("=== 专门测试销售相关接口 ===")
    
    # 基于销售的各种可能接口名称
    sales_interfaces = [
        # 基础格式
        "sales.trade.query",
        "sales.order.query", 
        "sales.query",
        
        # 带动作的格式
        "sales.trade.get",
        "sales.trade.list",
        "sales.trade.search",
        "sales.order.get",
        "sales.order.list", 
        "sales.order.search",
        
        # 不同分隔符
        "sales_trade_query",
        "sales_order_query",
        "sales-trade-query",
        "sales-order-query",
        
        # 驼峰格式
        "salesTradeQuery",
        "salesOrderQuery",
        "SalesTradeQuery",
        "SalesOrderQuery",
        
        # 带前缀
        "wms.sales.trade.query",
        "api.sales.trade.query",
        "erp.sales.trade.query",
        
        # 简化格式
        "trade.query",
        "order.query",
        "trade.get",
        "order.get",
        "trade.list",
        "order.list",
        
        # 中文拼音
        "xiaoshou.query",
        "dingdan.query",
        
        # 其他可能格式
        "getSalesOrder",
        "getSalesTrade", 
        "querySalesOrder",
        "querySalesTrade",
        "listSalesOrder",
        "listSalesTrade",
    ]
    
    client = WDTOriginalClient()
    
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    
    # 尝试不同的参数组合
    param_sets = [
        # 最小参数集
        {
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
        },
        # 带状态参数
        {
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'trade_status': 30,
        },
        # 带分页参数
        {
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'page_no': 0,
            'page_size': 1,
        },
        # 完整参数
        {
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'trade_status': 30,
            'page_no': 0,
            'page_size': 1,
        }
    ]
    
    promising_interfaces = []
    
    for interface in sales_interfaces:
        print(f"\n测试销售接口: {interface}")
        
        for i, params in enumerate(param_sets):
            try:
                response = client.call_api(interface, params)
                
                flag = response.get('flag')
                code = response.get('code')
                message = response.get('message', '')
                
                if flag == 'success':
                    print(f"  ✓ 参数集{i+1}: 成功！")
                    promising_interfaces.append((interface, params))
                    break
                elif "权限" in message:
                    print(f"  ⚠ 参数集{i+1}: 权限问题（接口存在）")
                    promising_interfaces.append((interface, params))
                    break
                elif "参数" in message and "错误" in message:
                    print(f"  ❓ 参数集{i+1}: 参数错误（接口可能存在）")
                    # 继续尝试其他参数集
                elif "接口" in message and "名字" in message:
                    print(f"  ✗ 参数集{i+1}: 接口不存在")
                    break  # 接口不存在，不用尝试其他参数
                else:
                    print(f"  ❓ 参数集{i+1}: {message}")
                    
            except Exception as e:
                print(f"  ✗ 参数集{i+1}: 异常 - {e}")
    
    if promising_interfaces:
        print(f"\n🎯 发现有希望的接口:")
        for interface, params in promising_interfaces:
            print(f"  - {interface}")
            print(f"    参数: {params}")
    
    return promising_interfaces


def test_alternative_approaches():
    """测试其他可能的方法"""
    print("\n=== 测试其他方法 ===")
    
    client = WDTOriginalClient()
    
    # 1. 尝试不带压缩的请求
    print("1. 测试不带压缩的请求:")
    try:
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        params = {
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'trade_status': 30,
        }
        
        response = client.call_api('sales.trade.query', params, compress_response=False)
        print(f"  结果: {response.get('flag')} - {response.get('message')}")
        
    except Exception as e:
        print(f"  失败: {e}")
    
    # 2. 尝试不同的时间格式
    print("\n2. 测试不同的时间格式:")
    time_formats = [
        '%Y-%m-%d %H:%M:%S',  # 当前格式
        '%Y/%m/%d %H:%M:%S',  # 斜杠格式
        '%Y-%m-%d',           # 只有日期
        '%Y%m%d %H%M%S',      # 紧凑格式
    ]
    
    for fmt in time_formats:
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            params = {
                'start_time': start_time.strftime(fmt),
                'end_time': end_time.strftime(fmt),
                'trade_status': 30,
            }
            
            response = client.call_api('sales.trade.query', params)
            print(f"  格式 {fmt}: {response.get('flag')} - {response.get('message')}")
            
        except Exception as e:
            print(f"  格式 {fmt}: 失败 - {e}")
    
    # 3. 尝试不同的状态值
    print("\n3. 测试不同的状态值:")
    status_values = [None, 5, 30, 0, 1, 2, 10, 20]
    
    for status in status_values:
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            params = {
                'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            }
            
            if status is not None:
                params['trade_status'] = status
            
            response = client.call_api('sales.trade.query', params)
            status_desc = f"状态{status}" if status is not None else "无状态"
            print(f"  {status_desc}: {response.get('flag')} - {response.get('message')}")
            
        except Exception as e:
            print(f"  状态{status}: 失败 - {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("开始深入测试接口发现...")
    print()
    
    # 1. 测试已知接口模式
    discovered_interfaces = test_known_working_interfaces()
    
    # 2. 专门测试销售接口
    sales_interfaces = test_sales_specific_interfaces()
    
    # 3. 测试其他方法
    test_alternative_approaches()
    
    print("\n" + "=" * 70)
    print("最终总结:")
    
    if discovered_interfaces:
        print(f"\n✓ 发现的可用接口 ({len(discovered_interfaces)} 个):")
        for interface in discovered_interfaces:
            print(f"  - {interface}")
    
    if sales_interfaces:
        print(f"\n🎯 有希望的销售接口 ({len(sales_interfaces)} 个):")
        for interface, params in sales_interfaces:
            print(f"  - {interface}")
    
    if not discovered_interfaces and not sales_interfaces:
        print("\n❌ 未发现任何可用的销售订单查询接口")
        print("\n💡 建议:")
        print("1. 确认文档对应的API端点是否正确")
        print("2. 联系旺店通技术支持确认接口名称")
        print("3. 检查账号权限和API版本")
        print("4. 考虑使用企业版API的sales_trade_query.php接口")
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
