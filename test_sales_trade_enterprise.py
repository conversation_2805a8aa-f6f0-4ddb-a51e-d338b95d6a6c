#!/usr/bin/env python3
"""
测试旺店通销售订单查询API（企业版）
"""
import logging
from datetime import datetime, timedelta

from wdt_sales_trade_enterprise_client import WDTSalesTradeEnterpriseClient, WDTSalesTradeEnterpriseAPIException
from config import WDTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_sales_trade_enterprise_api():
    """测试销售订单查询API（企业版）"""
    print("旺店通WMS销售订单查询API测试（企业版）")
    print("=" * 60)
    
    try:
        # 验证配置
        config = WDTConfig
        print("✓ 配置验证通过")
        print(f"  SID: {config.SID}")
        print(f"  APP_KEY: {config.APP_KEY}")
        print()
        
        # 创建客户端
        client = WDTSalesTradeEnterpriseClient(config)
        print("✓ 企业版销售订单客户端创建成功")
        print(f"  API端点: {client.base_url}")
        print()
        
        # 首先测试可用端点
        print("=== 检测可用端点 ===")
        available_endpoints = client.get_available_endpoints()
        
        if available_endpoints:
            print("✓ 找到可用端点:")
            for endpoint in available_endpoints:
                print(f"  - {endpoint}")
        else:
            print("✗ 未找到可用端点")
            return False
        
        print()
        
        # 测试销售订单查询
        print("=== 测试销售订单查询 ===")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        print(f"查询时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade_status=30,  # 待审核
                page_size=5
            )
            
            print("✓ 销售订单查询成功")
            
            # 解析响应
            data = response.get('data', {})
            
            # 检查不同的数据结构
            if 'order' in data:
                orders = data['order']
                total_count = data.get('total_count', len(orders))
            elif 'list' in data:
                orders = data['list']
                total_count = data.get('total_count', len(orders))
            elif 'trades' in data:
                orders = data['trades']
                total_count = data.get('total_count', len(orders))
            else:
                orders = data.get('data', [])
                total_count = data.get('total_count', len(orders))
            
            print(f"  总记录数: {total_count}")
            print(f"  当前页记录数: {len(orders)}")
            
            # 显示订单详情
            if orders:
                print("\n订单详情:")
                for i, trade in enumerate(orders[:3]):  # 只显示前3条
                    print(f"  订单 {i+1}:")
                    print(f"    订单号: {trade.get('trade_no', trade.get('order_no', 'N/A'))}")
                    print(f"    原始单号: {trade.get('src_tids', trade.get('src_tid', 'N/A'))}")
                    print(f"    状态: {trade.get('trade_status', trade.get('status', 'N/A'))}")
                    print(f"    金额: {trade.get('total_amount', trade.get('amount', 'N/A'))}")
                    print(f"    商品数量: {trade.get('goods_count', trade.get('item_count', 'N/A'))}")
                    print(f"    收货地址: {trade.get('receiver_area', trade.get('address', 'N/A'))}")
                    print(f"    创建时间: {trade.get('created', trade.get('create_time', 'N/A'))}")
                    print()
            else:
                print("  未找到订单记录")
            
            return True
            
        except WDTSalesTradeEnterpriseAPIException as e:
            print(f"✗ 销售订单查询失败: {e}")
            
            # 分析错误类型
            if e.code == 1090:
                print("  → 权限问题：需要申请接口权限")
            elif "参数" in str(e):
                print("  → 参数问题：检查参数格式")
            elif "时间" in str(e):
                print("  → 时间问题：检查时间格式或范围")
            else:
                print(f"  → 其他问题: {e}")
            
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_different_time_ranges():
    """测试不同的时间范围"""
    print("\n=== 测试不同时间范围 ===")
    
    client = WDTSalesTradeEnterpriseClient()
    
    # 测试不同的时间范围
    time_ranges = [
        ("最近1小时", timedelta(hours=1)),
        ("最近6小时", timedelta(hours=6)),
        ("最近1天", timedelta(days=1)),
        ("最近3天", timedelta(days=3))
    ]
    
    for name, delta in time_ranges:
        print(f"\n测试时间范围: {name}")
        
        end_time = datetime.now()
        start_time = end_time - delta
        
        try:
            response = client.query_sales_trades(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                page_size=1
            )
            
            data = response.get('data', {})
            
            # 获取记录数
            if 'order' in data:
                count = len(data['order'])
                total = data.get('total_count', count)
            elif 'list' in data:
                count = len(data['list'])
                total = data.get('total_count', count)
            else:
                count = len(data.get('data', []))
                total = data.get('total_count', count)
            
            print(f"  ✓ 成功，总记录数: {total}")
            
            if total > 0:
                return True  # 找到有数据的时间范围
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    return False


def main():
    """主函数"""
    setup_logging()
    
    print("开始测试旺店通销售订单查询API（企业版）...")
    print()
    
    # 基础测试
    success = test_sales_trade_enterprise_api()
    
    if not success:
        print("🔍 尝试不同时间范围...")
        test_different_time_ranges()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
