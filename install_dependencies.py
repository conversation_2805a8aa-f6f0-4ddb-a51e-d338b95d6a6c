#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安装Excel导出所需的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """安装所需依赖"""
    print("🔧 安装Excel导出所需依赖包")
    print("=" * 40)
    
    # 需要安装的包
    packages = [
        "pandas",      # 数据处理
        "openpyxl",    # Excel读写
        "xlsxwriter"   # Excel格式化（可选）
    ]
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果:")
    print(f"   成功: {success_count}/{len(packages)} 个包")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装成功！可以开始使用Excel导出功能。")
    else:
        print("⚠️ 部分依赖安装失败，可能影响Excel导出功能。")

if __name__ == "__main__":
    main()
